package marketing

import (
	"context"
	"strconv"
	"strings"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetFullReductionActivityLabelLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetFullReductionActivityLabelLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetFullReductionActivityLabelLogic {
	return &GetFullReductionActivityLabelLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetFullReductionActivityLabelLogic) GetFullReductionActivityLabel(req *types.GetFullReductionActivityLabelReq) (resp *types.GetFullReductionActivityLabelResp, err error) {
	var (
		result           types.GetFullReductionActivityLabelResp
		reduction        []Reduction
		special_activity []SpecialActivityInfo
		periods          []int64
	)
	resp = &result

	// 设置默认值
	result.Label = make(map[int64][]string)
	result.Activity = make(map[int64][]map[string]interface{})
	for _, v := range req.Data {
		periods = append(periods, v.Period)
		result.Label[v.Period] = make([]string, 0)
		result.Activity[v.Period] = make([]map[string]interface{}, 0)
	}

	// 当前时间戳
	timestamp := int64(time.Now().Unix())
	//查询开启的满减活动
	l.svcCtx.DbMarketing.Model(&reduction).
		Where("start_time <= ? and end_time > ? and is_enable = 1 and is_display_label = 1", timestamp, timestamp).
		Scan(&reduction)
	if len(reduction) == 0 {
		return
	}
	aid := make([]string, 0)
	for _, r := range reduction {
		if r.Type == 4 {
			aid = append(aid, strings.Split(r.Assign, ",")...)
		}
	}
	// 查询专题活动信息
	special_activity_goods := make(map[int64][]int64)
	if len(aid) > 0 {
		l.svcCtx.DbMarketing.Table("vh_special_activity sa").
			Joins("left join vh_special_activity_goods as sag on sag.activity_id = sa.id").
			Where("sa.id IN ? and sag.periods IN ?", aid, periods).
			Select("sa.id", "sag.periods").
			Order("sa.id desc").
			Scan(&special_activity)
		for _, v := range special_activity {
			special_activity_goods[v.Id] = append(special_activity_goods[v.Id], v.Periods)
		}
	}

	for _, v := range req.Data {
		for _, r := range reduction {
			var is_exist bool
			if r.TopImage != "" {
				r.TopImage = l.svcCtx.Config.ITEM.ALIURL + r.TopImage
			}

			switch r.Type {
			case 1: //闪购+秒发
				if v.PeriodType == 0 || v.PeriodType == 1 {
					is_exist = true
				}

			case 2: //闪购
				if v.PeriodType == 0 {
					is_exist = true
				}

			case 3: //秒发
				if v.PeriodType == 1 {
					is_exist = true
				}

			case 4: //指定活动
				assign := strings.Split(r.Assign, ",")
				for _, vv := range assign {
					if vv != "" {
						val, _ := strconv.ParseInt(vv, 10, 64)
						if gid, ok := special_activity_goods[val]; ok {
							if common.InArrayInt(v.Period, gid) {
								is_exist = true
							}
						}
					}
				}
			case 5: //指定商品
				assign := strings.Split(r.Assign, ",")
				for _, vv := range assign {
					val, _ := strconv.ParseInt(vv, 10, 64)
					if v.Period == val {
						is_exist = true
					}
				}
			}

			if is_exist {
				result.Label[v.Period] = append(result.Label[v.Period], r.Name)
				result.Activity[v.Period] = append(result.Activity[v.Period], map[string]interface{}{
					"id":                 r.Id,
					"name":               r.Name,
					"top_image":          r.TopImage,
					"top_image_skip_url": r.TopImageSkipUrl,
				})
			}
		}

		// 去重
		result.Label[v.Period] = common.ArrayDuplicateRemoval(result.Label[v.Period])
	}

	return
}
