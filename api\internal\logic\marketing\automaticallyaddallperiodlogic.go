package marketing

import (
	"context"

	"engine/api/internal/service/MarketingAutoAdd"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/config"

	"github.com/zeromicro/go-zero/core/logx"
)

type AutomaticallyAddAllPeriodLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAutomaticallyAddAllPeriodLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AutomaticallyAddAllPeriodLogic {
	return &AutomaticallyAddAllPeriodLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AutomaticallyAddAllPeriodLogic) AutomaticallyAddAllPeriod(req *types.AutomaticallyAddAllPeriodReq) error {
	service := MarketingAutoAdd.NewMarketingAutoAddLogic(l.svcCtx)
	go func() {
		for k, v := range config.PeriodTable {
			var info []CommonInfo
			l.svcCtx.DbCommodities.Table(v).
				Where("onsale_status = 2 and is_channel = 0").
				Select("id,title").Scan(&info)

			for _, vv := range info {
				if len(vv.Title) > 250 {
					vv.Title = vv.Title[:250]
				}
				body := types.AutomaticallyAddPeriodReq{
					Period:     vv.Id,
					PeriodType: int64(k),
					Title:      vv.Title,
					Card:       req.Card,
					Column:     req.Column,
					Label:      req.Label,
				}
				service.MarketingSectionAddPeriod(&body, 1)
			}
		}
	}()

	return nil
}
