syntax = "v1"

info(
    title: "秒发营销中台服务"
    author: "gangh"
    email: "<EMAIL>"
    version: "v3"
)

type (
    FiltersListReq {
        Page int64 `form:"page" validate:"required,min=1" v:"当前页"`
        Limit int64 `form:"limit" validate:"required,min=5" v:"返回条数"`
        Type string `form:"type,optional" validate:"omitempty" v:"标签类型"`
    }
    FiltersListResp {
        Total int64 `json:"total"`
        List []PeriodsSecondFilters `json:"list"`
    }
    PeriodsSecondFilters {
        Id int64 `json:"id"`
        FiltersType int64 `json:"filters_type"`
        Status int64 `json:"status"`
        Title string `json:"title"`
        Identifier string `json:"identifier"`
        WeightValue int64 `json:"weight_value"`
        Operator string `json:"operator"`
        UpdateTime string `json:"update_time"`
    }

    AddFiltersReq {
        Title string `json:"title" validate:"required" v:"标题"`
        WeightValue int64 `json:"weight_value" validate:"omitempty" v:"权重值"`
    }

    EditFiltersReq {
        Id int64 `json:"id" validate:"required,min=1" v:"ID"`
        Status int64 `json:"status" validate:"omitempty" v:"状态"`
        Title string `json:"title" validate:"required" v:"标题"`
        WeightValue int64 `json:"weight_value" validate:"omitempty" v:"权重值"`
    }

    UpdateFiltersReq {
        Id int64 `json:"id" validate:"required,min=1" v:"ID"`
        Status int64 `json:"status" validate:"omitempty" v:"状态"`
    }

    FiltersGoodsListReq {
        Page int64 `form:"page" validate:"required,min=1" v:"当前页"`
        Limit int64 `form:"limit" validate:"required,min=5" v:"返回条数"`
        FiltersId int64 `form:"filters_id" validate:"required,min=1" v:"筛选标签id"`
        Periods string `form:"periods,optional" validate:"omitempty" v:"期数"`
        Status string `form:"status,optional" validate:"omitempty" v:"状态"`
    }
    FiltersGoodsListResp {
        Total int64 `json:"total"`
        List []FiltersGoodsListData `json:"list"`
    }
    FiltersGoodsListData {
        Id int64 `json:"id"`
        FiltersId int64 `json:"filters_id"`
        Periods int64 `json:"periods"`
        PeriodsType int64 `json:"periods_type"`
        GoodsName string `json:"goods_name"`
        GoodsShortName string `json:"goods_short_name"`
        Images string `json:"images"`
        UnitPrice float64 `json:"unit_price"`
        MarketPrice float64 `json:"market_price"`
        Status int64 `json:"status"`
        WeightValue int64 `json:"weight_value"`
        UpdateName string `json:"update_name"`
        UpdateTime string `json:"update_time"`
    }

    AddFiltersGoodsReq{
        FiltersId int64 `json:"filters_id" validate:"required,min=1" v:"筛选标签id"`
        Periods int64 `json:"periods" validate:"required,min=1" v:"期数"`
        PeriodsType int64 `json:"periods_type" validate:"omitempty" v:"期数频道"`
        GoodsShortName string `json:"goods_short_name,optional" validate:"omitempty" v:"商品简称"`
        Status int64 `json:"status" validate:"omitempty" v:"状态"`
        WeightValue int64 `json:"weight_value" validate:"omitempty" v:"权重值"`
    }

    EditFiltersGoodsReq{
        Id int64 `json:"id" validate:"required,min=1" v:"商品记录id"`
        FiltersId int64 `json:"filters_id" validate:"required,min=1" v:"筛选标签id"`
        Periods int64 `json:"periods" validate:"required,min=1" v:"期数"`
        PeriodsType int64 `json:"periods_type" validate:"omitempty" v:"期数频道"`
        GoodsShortName string `json:"goods_short_name,optional" validate:"omitempty" v:"商品简称"`
        Status int64 `json:"status" validate:"omitempty" v:"状态"`
        WeightValue int64 `json:"weight_value" validate:"omitempty" v:"权重值"`
    }

    UpdateFiltersGoodsReq{
        Id int64 `json:"id" validate:"required,min=1" v:"商品记录id"`
        OperationType int64 `json:"operation_type" validate:"omitempty" v:"操作类型"`
        Value int64 `json:"value" validate:"omitempty" v:"操作值"`
    }

    DeleteFiltersGoodsReq{
        Id int64 `json:"id" validate:"required,min=1" v:"商品记录id"`
    }
)

@server(
    middleware: User
    group : second
    prefix :/commodities_server/v3/second
)

service main {
    @handler FiltersList //秒发筛选配置列表
    get /filters/list (FiltersListReq) returns (FiltersListResp)

    @handler AddFilters //添加秒发筛选配置
    post /filters/add (AddFiltersReq)

    @handler EditFilters //编辑秒发筛选配置
    post /filters/edit (EditFiltersReq)

    @handler UpdateFilters //更新秒发筛选配置
    post /filters/update (UpdateFiltersReq)

    @handler FiltersGoodsList //秒发筛选商品列表
    get /filters_goods/list (FiltersGoodsListReq) returns (FiltersGoodsListResp)

    @handler AddFiltersGoods //添加秒发筛选商品
    post /filters_goods/add (AddFiltersGoodsReq)

    @handler EditFiltersGoods //编辑秒发筛选商品
    post /filters_goods/edit (EditFiltersGoodsReq)

    @handler UpdateFiltersGoods //更新秒发筛选商品
    post /filters_goods/update (UpdateFiltersGoodsReq)

    @handler DeleteFiltersGoods //删除秒发筛选商品
    post /filters_goods/delete (DeleteFiltersGoodsReq)

}