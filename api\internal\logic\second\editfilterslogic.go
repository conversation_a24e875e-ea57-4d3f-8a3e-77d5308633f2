package second

import (
	"context"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type EditFiltersLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewEditFiltersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *EditFiltersLogic {
	return &EditFiltersLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *EditFiltersLogic) EditFilters(req *types.EditFiltersReq) error {
	var (
		count      int64
		show_count int64
		data       PeriodsSecondFilters
	)

	//操作人ID
	uid := l.ctx.Value("uid").(int64)
	//操作人
	user_name := l.ctx.Value("name").(string)
	//查询标签和标识是否存在
	l.svcCtx.DbCommodities.
		Model(&PeriodsSecondFilters{}).
		Where("id <> ? and title = ?", req.Id, req.Title).
		Count(&count)
	if count > 0 {
		return xerr.NewParamErrMsg("标签标题或标签标识已存在")
	}

	//查询标签和标识是否存在
	l.svcCtx.DbCommodities.Model(&data).Where("id = ?", req.Id).Scan(&data)
	if data.Id == 0 {
		return xerr.NewParamErrMsg("标签ID错误")
	}

	if req.Status == 1 && data.FiltersType == 1 {
		// 查询显示中商品数量
		l.svcCtx.DbCommodities.
			Model(&PeriodsSecondFiltersGoods{}).
			Where("filters_id = ? and status = 1", req.Id).
			Count(&show_count)
		if show_count == 0 {
			return xerr.NewParamErrMsg("无显示商品，标签不可生效")
		}
	}

	timed := int64(time.Now().Unix())
	info := map[string]interface{}{
		"status":       req.Status,
		"title":        req.Title,
		"weight_value": req.WeightValue,
		"operator_id":  uid,
		"operator":     user_name,
		"update_time":  timed,
	}
	err := l.svcCtx.DbCommodities.Model(&PeriodsSecondFilters{}).
		Where("id = ?", req.Id).
		Updates(info).Error
	if err != nil {
		return xerr.NewParamErrMsg(err.Error())
	}

	return nil
}
