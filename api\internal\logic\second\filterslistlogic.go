package second

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/xtime"

	"github.com/zeromicro/go-zero/core/logx"
)

type FiltersListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewFiltersListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *FiltersListLogic {
	return &FiltersListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *FiltersListLogic) FiltersList(req *types.FiltersListReq) (resp *types.FiltersListResp, err error) {
	var (
		data   []PeriodsSecondFilters
		count  int64
		list   []types.PeriodsSecondFilters
		result types.FiltersListResp
	)
	// 分页值
	offset := int((req.Page - 1) * req.Limit)

	query := l.svcCtx.DbCommodities.Model(&PeriodsSecondFilters{})
	// 筛选类型
	if req.Type != "" {
		query.Where("filters_type = ?", req.Type)
	}
	// 总条数
	query.Count(&count)

	// 列表
	query.Order("weight_value desc").Offset(offset).Limit(int(req.Limit)).Scan(&data)
	if len(data) > 0 {
		for _, v := range data {
			list = append(list, types.PeriodsSecondFilters{
				Id:          v.Id,
				FiltersType: v.FiltersType,
				Status:      v.Status,
				Title:       v.Title,
				Identifier:  v.Identifier,
				WeightValue: v.WeightValue,
				Operator:    v.Operator,
				UpdateTime:  xtime.Date(v.UpdateTime),
			})
		}
	}
	result = types.FiltersListResp{
		Total: count,
		List:  list,
	}

	return &result, nil
}
