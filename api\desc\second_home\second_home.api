syntax = "v1"

info(
    title: "秒发首页服务"
    author: "gangh"
    email: "<EMAIL>"
    version: "v3"
)

type (
    GetProductLabelReq {
        IsNewUser string `json:"is_new_user,optional" validate:"omitempty" v:"是否新用户"`
        Data []GetProductLabelReqData `json:"data" validate:"required" v:"期数数据"`
    }
    GetProductLabelReqData {
        Periods int64 `json:"periods" validate:"required,min=1" v:"期数"`
        PeriodsType int64 `json:"periods_type" validate:"required,min=0" v:"期数类型"`
    }
    GetProductLabelResp {
        List map[int64]GetProductLabelRespList `json:"list"`
    }
    GetProductLabelRespList {
        ProductLabel []ProductLabelTypes `json:"product_label"`
        TopLabel []ProductLabelTypes `json:"top_label"`
        LeftTopLabel []ProductLabelTypes `json:"left_top_label"`
    }
    ProductLabelTypes {
        Id int64 `json:"id"`
        Type int64 `json:"type"`
        Title string `json:"title"`
    }

    GetFullReductionLabelReq {
        Data []GetProductLabelReqData `json:"data" validate:"required" v:"期数数据"`
    }
    GetFullReductionLabelResp {
        List map[int64][]ProductLabelTypes `json:"list"`
    }

    AggregationReq {
        Client int64 `form:"client" validate:"omitempty,min=0" v:"广告客户端"`
        Mid string `form:"mid,optional" validate:"omitempty" v:"商家ID"`
        Longitude string `form:"longitude,optional" validate:"omitempty" v:"经度"`
        Latitude string `form:"latitude,optional" validate:"omitempty" v:"纬度"`
    }
    AggregationResp {
        HomeScreen []map[string]interface{} `json:"home_screen"`
        CollectTags map[string]interface{} `json:"collect_tags"`
        HotTopic []map[string]interface{} `json:"hot_topic"`
        Banner []map[string]interface{} `json:"banner"`
	    NewusersActivity map[string]interface{} `json:"newusers_activity"`
        SecondFiltersList []map[string]interface{} `json:"second_filters_list"`
    }

    GetContentReq {
        Page int64 `form:"page" validate:"required,min=1" v:"当前页"`
        Keywords string `form:"keywords" validate:"required" v:"关键字"`
    }
    GetContentResp {
        Total int64 `json:"total"`
        List []GetContentRespList `json:"list"`
    }
    GetContentRespList{
        Genre string `json:"genre"`
        Post map[string]interface{} `json:"post"`
        News map[string]interface{} `json:"news"`
        Wine map[string]interface{} `json:"wine"`
    }

    AggregationV2Req {
        Client int64 `form:"client" validate:"omitempty,min=0" v:"广告客户端"`
        Mid string `form:"mid,optional" validate:"omitempty" v:"商家ID"`
        Longitude string `form:"longitude,optional" validate:"omitempty" v:"经度"`
        Latitude string `form:"latitude,optional" validate:"omitempty" v:"纬度"`
        Uuid string `form:"mid,optional" validate:"omitempty" v:"uuid"`
    }
    AggregationV2Resp {
        Column []map[string]interface{} `json:"column"`
        Card []map[string]interface{} `json:"card"`
    }
)

@server(
    middleware: Global
    group : second_home
    prefix :/commodities_server/v3/second_home
)

service main {
    @handler GetProductLabel //获取产品标签
    post /get_product_label (GetProductLabelReq) returns (GetProductLabelResp)

    @handler GetFullReductionLabel //获取产品满减标签
    post /get_full_reduction_label (GetFullReductionLabelReq) returns (GetFullReductionLabelResp)

    @handler Aggregation //获取秒发首页聚合
    get /aggregation (AggregationReq) returns (AggregationResp)

    @handler GetContent //获取秒发首页搜索内容
    get /get_content (GetContentReq) returns (GetContentResp)

    @handler AggregationV2 //获取秒发首页聚合V2
    get /aggregation_v2 (AggregationV2Req) returns (AggregationV2Resp)
}