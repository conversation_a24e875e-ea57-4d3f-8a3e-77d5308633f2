package second_home

import (
	"context"
	"encoding/json"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common"
	"fmt"
	"sort"
	"strings"

	"github.com/gomodule/redigo/redis"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
)

type GetContentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetContentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetContentLogic {
	return &GetContentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

type UserInfos struct {
	Data struct {
		List []UserInfo `json:"list"`
	} `json:"data"`
}
type UserInfo struct {
	Uid         string `json:"uid"`
	Nickname    string `json:"nickname"`
	AvatarImage string `json:"avatar_image"`
	UserLevel   string `json:"user_level"`
	UserType    string `json:"type"`
}

var (
	redis_key        string
	redis_expire_key string
	limit            int64
)

type RedisReply struct {
	Total int64
	Data  []map[string]interface{}
}

func (l *GetContentLogic) GetContent(req *types.GetContentReq) (c *types.GetContentResp, err error) {
	var (
		data     []map[string]interface{}
		total    int
		pageData []map[string]interface{}
	)
	//用户ID
	uid := l.ctx.Value("uid").(int64)
	redis_key = fmt.Sprintf("vinehoo.second.home.content.%s.%v", req.Keywords, uid)
	redis_expire_key = redis_key + ".expire"
	// 返回条数
	limit = 15
	//查询缓存数据
	res, err := l.QueryCache(req)

	//缓存为空重新查询
	if err != nil {
		data = *l.QueryContent(req)
		//按传入的页码, 分页取出数据并返回
		total := len(data)
		//  分页开始位置
		start := (int(req.Page) - 1) * int(limit)
		//  分页结束位置
		end := int(limit) * int(req.Page)
		//  如果结束位置大于数据总数，将结束位置设置为数组末尾
		if end > total {
			end = total
		}
		if start < end {
			//  分页数据
			pageData = make([]map[string]interface{}, end-start)
			copy(pageData, data[start:end])
		}

	} else {
		total = int(res.Total)
		pageData = res.Data
	}

	//定义返回数据
	result := types.GetContentResp{
		Total: int64(total),
		List:  make([]types.GetContentRespList, 0),
	}

	//数据类型分配
	if len(pageData) > 0 {
		for _, v := range pageData {
			genre := fmt.Sprintf("%v", v["type"])
			post := make(map[string]interface{})
			news := make(map[string]interface{})
			wine := make(map[string]interface{})
			switch fmt.Sprintf("%v", v["type"]) {
			case "post":
				post = v
			case "news":
				news = v
			case "wine":
				wine = v
			}
			result.List = append(result.List, types.GetContentRespList{
				Genre: genre,
				Post:  post,
				News:  news,
				Wine:  wine,
			})
		}
	}

	return &result, nil
}

// 查询内容数据
func (l *GetContentLogic) QueryCache(req *types.GetContentReq) (*RedisReply, error) {
	//查询缓存
	reply, err := l.svcCtx.RDbSix.Invoke(func(conn redis.Conn) (interface{}, error) {
		var (
			total int64
			data  []map[string]interface{}
			reply RedisReply
		)
		//查询是否过期
		expire, _ := redis.String(conn.Do("GET", redis_expire_key))
		if expire != "" {
			//总条数
			total, _ = redis.Int64(conn.Do("ZCARD", redis_key))
			if total > 0 {
				//  分页开始位置
				start := ((int(req.Page) - 1) * int(limit))
				//  分页结束位置
				end := (int(limit) * int(req.Page)) - 1
				//  如果结束位置大于数据总数，将结束位置设置为数组末尾
				if end > int(total) {
					end = int(total)
				}
				//  如果结束位置大于数据总数，将结束位置设置为数组末尾
				if start < end {
					str, _ := redis.Strings(conn.Do("ZRANGE", redis_key, start, end, "WITHSCORES"))
					for k, v := range str {
						if k%2 == 0 {
							row := make(map[string]interface{})
							json.Unmarshal([]byte(v), &row)
							data = append(data, row)
						}
					}
				}
			}

		} else {
			conn.Do("DEL", redis_key)
			return reply, fmt.Errorf("del")
		}
		reply = RedisReply{
			Total: total,
			Data:  data,
		}
		return reply, nil
	})

	result := reply.(RedisReply)

	return &result, err
}

// 查询内容数据
func (l *GetContentLogic) QueryContent(req *types.GetContentReq) *[]map[string]interface{} {
	var (
		posts     []map[string]interface{}
		wine      []map[string]interface{}
		news      []map[string]interface{}
		uids      []string
		userinfos UserInfos
	)
	//判断缓存是否存在 如果不存在 则查询出数据组装好写入缓存, 如果存在 直接进入 setp 7

	//用户ID
	uid := l.ctx.Value("uid").(int64)
	//region 查询出3个类型的数据
	mr.Finish(func() (err error) {
		//帖子
		if uid > 0 {
			//已登录
			post_where := "`content` LIKE ? AND ( `status` = 1 OR ( `uid` = ? AND `status` IN ( 0, 4 ) ) )"
			l.svcCtx.DbCommunity.Table("vh_posts").
				Where(post_where, "%"+req.Keywords+"%", uid).
				Select("id", "content as title", "type_data as banner_img", "uid", "viewnums as pageviews").
				Order("id desc").
				Scan(&posts)
		} else {
			//未登录
			post_where := "`content` LIKE ? AND `status` = 1"
			l.svcCtx.DbCommunity.Table("vh_posts").
				Where(post_where, "%"+req.Keywords+"%").
				Select("id", "content as title", "type_data as banner_img", "uid", "viewnums as pageviews").
				Order("id desc").
				Scan(&posts)
		}
		for k, v := range posts {
			posts[k]["type"] = "post"
			uid := fmt.Sprintf("%v", v["uid"])
			if uid != "0" && uid != "" {
				uids = append(uids, uid)
			}
		}
		return
	}, func() (err error) {
		//酒评
		if uid > 0 {
			//已登录
			wine_where := "`wine_evaluation` LIKE ? AND ( `status` = 1 OR ( `uid` = ? AND `status` IN ( 0, 4 ) ) )"
			l.svcCtx.DbCommunity.Table("vh_wine_evaluation").
				Where(wine_where, "%"+req.Keywords+"%", uid).
				Select("id", "wine_evaluation as title", "type_data as banner_img", "uid", "grade as scoring").
				Order("id desc").
				Scan(&wine)
		} else {
			//未登录
			wine_where := "`wine_evaluation` LIKE ? AND `status` = 1"
			l.svcCtx.DbCommunity.Table("vh_wine_evaluation").
				Where(wine_where, "%"+req.Keywords+"%").
				Select("id", "wine_evaluation as title", "type_data as banner_img", "uid", "grade as scoring").
				Order("id desc").
				Scan(&wine)
		}
		for k, v := range wine {
			wine[k]["type"] = "wine"
			uid := fmt.Sprintf("%v", v["uid"])
			if uid != "0" && uid != "" {
				uids = append(uids, uid)
			}
		}
		return
	}, func() (err error) {
		//酒闻
		l.svcCtx.DbNews.Table("vh_article").Where("status = 1 AND title like ?", "%"+req.Keywords+"%").
			Select("id", "title", "`img` as `banner_img`", "uid", "`viewnums` as `pageviews`").
			Order("id desc").
			Scan(&news)
		for k, v := range news {
			news[k]["type"] = "news"
			uid := fmt.Sprintf("%v", v["uid"])
			if uid != "0" && uid != "" {
				uids = append(uids, uid)
			}

		}
		return
	})
	//将3个数组数据组装为一个完整的数组 (有规律的打乱数据顺序)
	data := append(posts, wine...)
	data = append(data, news...)
	sort.Slice(data, func(i, j int) bool {
		return (i%3)*3+i/3 < (j%3)*3+j/3
	})

	user := make(map[string]UserInfo)
	if len(uids) > 0 {
		// 将全部 uid 拿到,(去重)查询出用户数据
		url := l.svcCtx.Config.ITEM.USER_URL + "/user/v3/profile/getUserInfo"
		body := map[string]string{
			"uid":   common.ArrayToString(uids, ","),
			"field": "uid,nickname,avatar_image,user_level,type",
		}
		header := map[string]string{}
		res, _ := l.svcCtx.HttpClient.PostJson(url, body, header)
		json.Unmarshal(res.Body(), &userinfos)
		if len(userinfos.Data.List) > 0 {
			for _, v := range userinfos.Data.List {
				user[v.Uid] = v
			}
		}
	}

	//将user数据组装进数组
	for k, v := range data {
		if fmt.Sprintf("%v", v["banner_img"]) != "" {
			if !strings.Contains(fmt.Sprintf("%v", v["banner_img"]), l.svcCtx.Config.ITEM.ALIURL) {
				v["banner_img"] = fmt.Sprintf("%v%v", l.svcCtx.Config.ITEM.ALIURL, v["banner_img"])
			}
		}
		data[k]["user_img"] = ""
		data[k]["user_name"] = ""
		data[k]["user_level"] = ""
		data[k]["user_type"] = ""
		if vv, ok := v["uid"]; ok {
			uid := fmt.Sprintf("%v", vv)
			if vvv, ok := user[uid]; ok {
				if vvv.AvatarImage != "" {
					data[k]["user_img"] = l.svcCtx.Config.ITEM.ALIURL + vvv.AvatarImage
				}
				data[k]["user_name"] = vvv.Nickname
				data[k]["user_level"] = vvv.UserLevel
				data[k]["user_type"] = vvv.UserType
			}
		}
	}
	//将总的数组存入Redis  设置过期时间 5分钟
	go l.svcCtx.RDbSix.Invoke(func(conn redis.Conn) (interface{}, error) {
		for k, v := range data {
			body_byte, _ := json.Marshal(v)
			conn.Send("ZADD", redis_key, k+1, string(body_byte))
		}
		conn.Flush()
		defer func() {
			conn.Do("SETEX", redis_expire_key, 300, "1")
		}()
		return conn.Receive()
	})

	return &data
}
