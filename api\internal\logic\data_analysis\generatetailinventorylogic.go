package data_analysis

import (
	"context"

	"engine/api/command/tail_inventory"
	"engine/api/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GenerateTailInventoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGenerateTailInventoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GenerateTailInventoryLogic {
	return &GenerateTailInventoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GenerateTailInventoryLogic) GenerateTailInventory() error {
	go tail_inventory.NewTailInventoryLogic(
		l.svcCtx.DbCommodities,
		l.svcCtx.DbWms,
		l.svcCtx.Config,
		true,
	)

	return nil
}
