package second

import (
	"context"
	"strings"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/xtime"

	"github.com/zeromicro/go-zero/core/logx"
)

type FiltersGoodsListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewFiltersGoodsListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *FiltersGoodsListLogic {
	return &FiltersGoodsListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *FiltersGoodsListLogic) FiltersGoodsList(req *types.FiltersGoodsListReq) (resp *types.FiltersGoodsListResp, err error) {
	var (
		goods      []PeriodsSecondFiltersGoods
		count      int64
		table_name string
	)
	list := make([]types.FiltersGoodsListData, 0)
	// 分页值
	offset := int((req.Page - 1) * req.Limit)
	query := l.svcCtx.DbCommodities.Model(&goods).Where("filters_id = ?", req.FiltersId)
	// 期数筛选
	if req.Periods != "" {
		query.Where("periods = ?", req.Periods)
	}
	// 期数筛选
	if req.Status != "" {
		query.Where("status = ?", req.Status)
	}
	// 总条数
	query.Count(&count)
	// 列表
	query.Order("id desc").Offset(offset).Limit(int(req.Limit)).Scan(&goods)
	if len(goods) > 0 {
		// 商品期数分类
		periods_class := make(map[int64][]int64)
		for _, v := range goods {
			if _, ok := periods_class[v.PeriodsType]; !ok {
				periods_class[v.PeriodsType] = make([]int64, 0)
			}
			periods_class[v.PeriodsType] = append(periods_class[v.PeriodsType], v.Periods)
		}
		//查询商品信息
		periods_info := make(map[int64]PeriodsData)
		for k, v := range periods_class {
			var periods_data []PeriodsData
			switch k {
			case 0: //闪购
				table_name = "vh_periods_flash"
			case 1: //秒发
				table_name = "vh_periods_second"
			}
			l.svcCtx.DbCommodities.
				Table(table_name).
				Where("id IN ?", v).
				Select("id", "title", "brief", "product_img", "price", "market_price").
				Scan(&periods_data)
			for _, g := range periods_data {
				periods_info[g.Id] = g
			}
		}

		// 组合数据
		for _, v := range goods {
			images := ""
			// 匹配商品信息
			goodsinfo := PeriodsData{}
			if g, ok := periods_info[v.Periods]; ok {
				goodsinfo = g
				if g.ProductImg != "" {
					product_img := strings.Split(g.ProductImg, ",")
					for _, img := range product_img {
						if img != "" {
							images = l.svcCtx.Config.ITEM.ALIURL + img
							break
						}
					}
				}
			}

			list = append(list, types.FiltersGoodsListData{
				Id:             v.Id,
				FiltersId:      req.FiltersId,
				Periods:        v.Periods,
				PeriodsType:    v.PeriodsType,
				GoodsName:      goodsinfo.Title,
				GoodsShortName: v.GoodsShortName,
				Images:         images,
				UnitPrice:      goodsinfo.Price,
				MarketPrice:    goodsinfo.MarketPrice,
				Status:         v.Status,
				WeightValue:    v.WeightValue,
				UpdateName:     v.UpdateName,
				UpdateTime:     xtime.Date(v.UpdateTime),
			})
		}

	}

	resp = &types.FiltersGoodsListResp{
		Total: count,
		List:  list,
	}

	return
}
