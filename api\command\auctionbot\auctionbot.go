package auctionbot

import (
	"encoding/json"
	"engine/common"
	"engine/common/config"
	"engine/common/httpClient"
	"engine/common/redigo"
	"engine/common/xtime"
	"fmt"
	"math"
	"math/rand"
	"sync"
	"time"

	"github.com/gomodule/redigo/redis"
	"gorm.io/gorm"
)

var (
	//随机省
	ProvinceName = []string{
		"北京",
		"上海",
		"广东",
		"重庆",
		"四川",
		"江苏",
		"浙江",
		"黑龙江",
	}
	// 出价频率（秒）
	BidFrequency = 60
	// 当前时间戳
	Dtime int64
)

/*拍品自动顶价机器人*/
type AuctionBotLogic struct {
	Config     config.ApiConfig
	Db         *gorm.DB
	DbUser     *gorm.DB
	Rdb        *redigo.ClientConfig
	HttpClient *httpClient.HttpConfig
}

func NewAuctionBotLogic(db *gorm.DB, db_user *gorm.DB, Config config.ApiConfig, rdb *redigo.ClientConfig) {
	l := &AuctionBotLogic{
		Config:     Config,
		Db:         db,
		DbUser:     db_user,
		Rdb:        rdb,
		HttpClient: httpClient.Handle(Config),
	}
	// 运行
	go l.Run()
}

// 运行
func (l *AuctionBotLogic) Run() {
	for {
		//当前时间戳
		Dtime = int64(time.Now().Unix())

		// 执行处理
		l.Execute()

		//1分钟执行一次
		// time.Sleep(1 * time.Minute)
		frequency := rand.Intn(31) + 30
		time.Sleep(time.Duration(frequency) * time.Second)
	}
}

// 执行处理
func (l *AuctionBotLogic) Execute() {
	var (
		goods []Goods
		user  map[int64]VestUserInfo
		wg    sync.WaitGroup
	)

	wg.Add(1)
	go func() {
		defer wg.Done()
		//获取所有马甲用户
		user = *l.GetAllVestUser()
	}()
	wg.Add(1)
	go func() {
		defer wg.Done()
		// 查询竞拍中拍品
		l.Db.Model(&goods).Where("onsale_status = 2 and is_use_robots = 1 and label = 0").Scan(&goods)
	}()
	wg.Wait()
	ch := make(chan int, 300)
	for _, v := range goods {
		// 过滤截止时间大于当前时间拍品
		if v.ClosingAuctionTime <= Dtime {
			continue
		}

		//拍品处理数据
		data := ProcessingData{
			// 拍品数据
			Gs: v,
			// 所有马甲用户
			User: user,
		}

		//拍品处理
		ch <- 1
		go l.AuctionProcessing(&data, ch)

	}
}

// 执行处理
func (l *AuctionBotLogic) AuctionProcessing(data *ProcessingData, ch chan int) {
	var (
		bid_price    int64
		target_price float64
		exe_num      int
		num          int
		deadline     int64
		frequency    int
		bid_type     int64
	)
	defer func() {
		<-ch
	}()

	//商品数据
	g := data.Gs

	//成本价(目标价格)
	target_price = g.CostPrice

	//判断结束3分钟内拍品
	if (data.Gs.ClosingAuctionTime - (3 * 60)) <= Dtime {
		//结束3分钟内拍品
		bid_type = 1
		//机器人运行截止时间
		deadline = g.ClosingAuctionTime

		//结束时间大于3分钟
	} else {
		//结束时间大于3分钟
		bid_type = 2
		//机器人运行截止时间
		deadline = g.ClosingAuctionTime - (3 * 60)
	}

	// 到结束脚本最大能执行次数
	exe_num = int(math.Ceil(float64(deadline-Dtime) / float64(BidFrequency)))

	// 当前竞拍价
	current_bid_price := l.GetBidPrice(g.Id)
	// 大于目标价直接跳过
	if current_bid_price >= target_price {
		return
	}

	// 查询出价记录
	record := *l.GetBidRecord(g.Id)
	if len(record) > 0 {
		var count int64
		new_record := record[0]

		// 最新出价用户为马甲用户不再出价
		l.Db.Table("vh_vest_user").Where("uid = ?", new_record.Uid).Count(&count)
		if count > 0 {
			return
		}

		// 最新出价时间
		newbidtime := time.Unix(new_record.CreateTime, 0).Format("2006-01-02 15:04")
		// 当前时间
		dtime := time.Unix(Dtime, 0).Format("2006-01-02 15:04")

		// 最新出价时间等于当前时间不在出价
		if exe_num > 1 && newbidtime == dtime {
			return
		}
	}

	//目标价格相差金额（目标价格-当前竞拍价）
	diff_price := common.Bcsub(target_price, current_bid_price, 2)

	if len(record) == 0 { //无出价记录
		// 判断最大还需顶价几次（相差金额/加价幅度）
		num = int(math.Ceil((diff_price - g.Price) / g.Markup))

	} else {
		// 判断最大还需顶价几次（相差金额/加价幅度）
		num = int(math.Ceil(diff_price / g.Markup))
	}

	/*出价频率控制*/
	result := l.BidFrequency(exe_num, num, deadline, g.Id, bid_type)
	if !result {
		fmt.Println(g.Id, "过滤")
		return
	}

	if len(record) > 0 { // 有出价记录
		// if num > exe_num {
		// 	// 出价次数
		// num = exe_num
		// 	if num < 2 {
		// 		//目标加价幅度
		// 		markup := common.Bcdev(diff_price, float64(exe_num), 2)
		// 		//加价幅度
		// 		g.Markup = common.BcMul(math.Ceil(markup/g.Markup), g.Markup, 2)
		// 	}
		// }

		// 倍率出价1倍
		frequency = 1

		// 截拍1个小时之前：1-5倍加价幅度随机
		if (data.Gs.ClosingAuctionTime - (60 * 60)) > Dtime {
			// 倍率判断33%概率出价2倍
			if num >= 2 && rand.Intn(5) == 1 {
				frequency = 2
			}
			// 倍率判断15%概率出价3倍
			if num >= 3 && rand.Intn(7) == 1 {
				frequency = 3
			}
			// 倍率判断10%概率出价4倍
			if num >= 4 && rand.Intn(10) == 1 {
				frequency = 4
			}
			// 倍率判断5%概率出价5倍
			if num >= 5 && rand.Intn(20) == 1 {
				frequency = 5
			}
			// 倍率判断4%概率直接出满
			if num <= 5 && rand.Intn(25) == 1 {
				frequency = num
			}
		}

		// 出价金额((随机次数*加价幅度)+当前竞拍价)
		bid_price = int64(math.Ceil(
			common.Bcadd(
				common.BcMul(float64(frequency), g.Markup, 2),
				current_bid_price, 2)))

	} else { // 无出价记录使用起拍价
		bid_price = int64(g.Price)
	}

	// 获取随机马甲用户
	random_user := l.GetRandomUser(&record, data)
	// 出价URL：https://showdoc.wineyun.com/web/#/89/3949
	url := l.Config.ITEM.AUVTION_URL + "/auction-goods/v3/goods/vestBid"
	// 提交马甲出价
	body := map[string]interface{}{
		"id":            g.Id,
		"uid":           random_user.Uid,
		"nickname":      random_user.Nickname,
		"bid_price":     bid_price,
		"is_anonymous":  0,
		"timing":        xtime.Date(Dtime),
		"province_name": random_user.ProvinceName,
		"avatar_image":  random_user.AvatarImage,
	}
	fmt.Println(body)
	l.HttpClient.PostJson(url, body, map[string]string{})

	//增加围观量
	go l.Db.Debug().Model(&g).UpdateColumn("pageviews", gorm.Expr("pageviews + ?", 1))

	fmt.Println(g.Id, "OK")
}

// 获取随机一条用户信息
func (l *AuctionBotLogic) GetRandomUser(record *[]BidRecord, data *ProcessingData) *VestUserInfo {
	var users []VestUserInfo

	bid_record := *record

	for _, v := range data.User {
		//过滤最近一次出价用户
		if len(bid_record) > 0 && fmt.Sprintf("%d", v.Uid) == bid_record[0].Uid {
			continue
		}
		users = append(users, v)
	}

	// 随机索引
	randomIndex := rand.Intn(len(users))
	// 随机用户
	randomUser := users[randomIndex]

	//获取出价记录地区
	if len(bid_record) > 0 {
		for _, v := range bid_record {
			if fmt.Sprintf("%d", randomUser.Uid) == v.Uid {
				randomUser.ProvinceName = v.ProvinceName
			}
		}
	}

	return &randomUser
}

// 查询拍品当前竞拍价
func (l *AuctionBotLogic) GetBidPrice(id int64) float64 {
	reply, _ := redis.Float64(l.Rdb.Invoke(func(conn redis.Conn) (interface{}, error) {
		key := fmt.Sprintf("auction_%d_bid_price", id)
		return conn.Do("GET", key)
	}))

	return reply
}

// 查询拍品当前竞拍最新出价记录
func (l *AuctionBotLogic) GetBidRecord(id int64) *[]BidRecord {
	var (
		record     BidRecord
		bid_record []BidRecord
	)
	reply, err := redis.Strings(l.Rdb.Invoke(func(conn redis.Conn) (interface{}, error) {
		key := fmt.Sprintf("auction_%d_history", id)
		return conn.Do("LRANGE", key, 0, -1)
	}))

	if err == nil {
		for _, v := range reply {
			json.Unmarshal([]byte(v), &record)
			bid_record = append(bid_record, record)
		}
	}
	return &bid_record
}

// 获取所有马甲用户
func (l *AuctionBotLogic) GetAllVestUser() *map[int64]VestUserInfo {
	var vest_user []VestUser

	user_info := make(map[int64]VestUserInfo)

	// 查询拍卖马甲用户
	l.Db.Table("vh_vest_user").Order("RAND()").Limit(10).Scan(&vest_user)

	for _, v := range vest_user {
		if v.Region == "" {
			// 随机索引
			randomIndex := rand.Intn(len(ProvinceName))
			// 随机省份
			province_name := ProvinceName[randomIndex]
			v.Region = province_name
			//修改地区
			l.Db.Table("vh_vest_user").Where("uid = ?", v.Uid).Updates(map[string]interface{}{
				"region": province_name,
			})
		}
		// 头像
		if v.AvatarImage != "" {
			v.AvatarImage = l.Config.ITEM.ALIURL + v.AvatarImage
		}

		user_info[v.Uid] = VestUserInfo{
			Uid:          v.Uid,
			Nickname:     v.Nickname,
			AvatarImage:  v.AvatarImage,
			ProvinceName: v.Region,
		}
	}
	return &user_info
}

// 出价频率控制
func (l *AuctionBotLogic) BidFrequency(exe_num, num int, deadline, goods_id, bid_type int64) bool {
	var (
		bid_alendar []string
		suffix      string
	)

	switch bid_type {
	case 1: //结束前3分钟
		return true

	case 2: //结束大于3分钟
		suffix = "thirty"
	}
	key := fmt.Sprintf("auction_robot_bid_alendar:%s.%d", suffix, goods_id)

	//查询出价日历缓存
	reply, _ := redis.String(l.Rdb.Invoke(func(conn redis.Conn) (interface{}, error) {
		return conn.Do("GET", key)
	}))
	if reply == "" {
		date := xtime.SecTimeBefore(Dtime, deadline)
		if num > exe_num {
			num = exe_num
		}
		if num > len(date) {
			bid_alendar = date
		} else {
			//计算出价日历缓存
			bid_alendar = common.Shuffle(date, num)
		}

		if len(bid_alendar) > 0 {
			redis.String(l.Rdb.Invoke(func(conn redis.Conn) (interface{}, error) {
				bt, _ := json.Marshal(bid_alendar)
				return conn.Do("SETEX", key, 10*86400, string(bt))
			}))
		}
	} else {
		json.Unmarshal([]byte(reply), &bid_alendar)
	}

	//出价日历为空
	if len(bid_alendar) == 0 {
		return false
	}

	//当前时间
	dtime := time.Unix(Dtime, 0).Format("2006-01-02 15:04")
	//验证当前时间是否在出价日历中
	result := common.InArrayStr(dtime, bid_alendar)

	//获取大于当前时间的数量
	// if result {
	// 	dTime, _ := time.Parse("2006-01-02 15:04", dtime)
	// 	for _, v := range bid_alendar {
	// 		timeObj, _ := time.Parse("2006-01-02 15:04", v)
	// 		if timeObj.After(dTime) {
	// 			count++
	// 		}
	// 	}
	// }

	return result
}
