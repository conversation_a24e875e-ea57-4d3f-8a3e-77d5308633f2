package redigo

import (
	"engine/common/config"
	"engine/common/logger"
	"fmt"
	"time"

	"github.com/gomodule/redigo/redis"
)

var (
	Pool *redis.Pool
)

type ClientConfig struct {
	Pool *redis.Pool
}

func Handle(c config.RedisCfg, db, MaxIdleConns int) *ClientConfig {
	return &ClientConfig{
		Pool: Connect(c, db, MaxIdleConns),
	}
}

func (c *ClientConfig) Invoke(f func(conn redis.Conn) (interface{}, error)) (interface{}, error) {
	var (
		conn  redis.Conn
		reply interface{}
		err   error
	)

	conn = c.Pool.Get()
	if err = conn.Err(); err != nil {
		return nil, err
	}
	defer func(conn redis.Conn) {
		if err = conn.Close(); err != nil {
			logger.E("Redis conn close Error:", err)
		}

	}(conn)

	reply, err = f(conn)
	if err != nil {
		logger.E("Redis Error:", err)
	}
	return reply, err
}

func Connect(cfg config.RedisCfg, db, MaxIdleConns int) *redis.Pool {
	return &redis.Pool{
		Dial: func() (redis.Conn, error) {
			conn, err := redis.Dial(
				"tcp",
				fmt.Sprintf("%s:%d",
					cfg.Host,
					cfg.Port,
				),
				// redis.DialReadTimeout(10 * time.Second), //要使用发布订阅时，需要关闭读取时间限制
				// redis.DialWriteTimeout(10 * time.Second),
				redis.DialConnectTimeout(10*time.Second),
				redis.DialDatabase(db),
				redis.DialPassword(cfg.Password),
			)
			if err != nil {
				logger.E("Redis Conn Error:", err)
				panic(err)
			}
			return conn, err
		},
		MaxIdle:     MaxIdleConns,     //最大空闲数
		MaxActive:   MaxIdleConns,     //最大连接数
		IdleTimeout: 10 * time.Second, //多少时间后关闭空闲连接
	}
}
