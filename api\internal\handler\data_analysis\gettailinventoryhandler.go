package data_analysis

import (
	"engine/api/internal/logic/data_analysis"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func GetTailInventoryHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetTailInventoryReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := data_analysis.NewGetTailInventoryLogic(r.Context(), svcCtx)
		resp, err := l.GetTailInventory(&req)
		result.HttpResult(r, w, resp, err)
	}
}
