package esClient

import (
	"context"
	"encoding/json"
	"engine/common/config"
	"engine/common/logger"
	"engine/common/xerr"
	"fmt"

	"github.com/olivere/elastic/v7"
)

type Esconfig struct {
	Client *elastic.Client
	Prefix string
	Fields []string
	Index  string
	From   int64
	Size   int64
	Sort   []map[string]string
	Query  map[string]map[string]interface{}
}

func Handle(Host, username, password, prefix string) *Esconfig {
	return &Esconfig{
		Client: NewEsClient(Host, username, password),
		Prefix: prefix,
	}
}

func New(c config.ApiConfig) *Esconfig {
	return &Esconfig{
		Client: NewEsClient(c.Es.Host, c.Es.Username, c.Es.Password),
		Prefix: c.Es.Prefix,
	}
}

func NewEsClient(Host, username, password string) *elastic.Client {
	client, err := elastic.NewClient(
		elastic.SetURL(Host),
		elastic.SetSniff(false),
		elastic.SetBasicAuth(username, password),
	)
	if err != nil {
		panic(fmt.Errorf("EsClient Error %s", err.Error()))
	}
	return client
}

// 设置索引
func (e *Esconfig) Name(index string) *Esconfig {
	e.Index = index
	return e
}

func (e *Esconfig) Where(param [][]interface{}) *Esconfig {
	filter := make([]map[string]interface{}, 0)
	must := make([]map[string]interface{}, 0)
	must_not := make([]map[string]interface{}, 0)

	for _, v := range param {
		if len(v) == 3 {
			switch v[1] {
			case "=": //匹配查询 ['name','=','张三']
				must = append(must, map[string]interface{}{
					"match": map[string]interface{}{
						fmt.Sprintf("%v", v[0]): v[2],
					},
				})
			case "==": //分词强匹配查询 ['name','==','张三']
				must = append(must, map[string]interface{}{
					"match_phrase": map[string]interface{}{
						fmt.Sprintf("%v", v[0]): v[2],
					},
				})
			case "===": //查询 ['name','===','张三']
				must = append(must, map[string]interface{}{
					"term": map[string]interface{}{
						fmt.Sprintf("%v", v[0]): v[2],
					},
				})
			case "<>": //不等于查询 ['name','<>','张三']
				must_not = append(must_not, map[string]interface{}{
					"match": map[string]interface{}{
						fmt.Sprintf("%v", v[0]): v[2],
					},
				})
			case "in": //查找多个精准值 ['id','in',[1,2]]
				filter = append(filter, map[string]interface{}{
					"terms": map[string]interface{}{
						fmt.Sprintf("%v", v[0]): v[2],
					},
				})
			case "not in": //查找多个精准值 ['id','in',[1,2]]
				must_not = append(must_not, map[string]interface{}{
					"terms": map[string]interface{}{
						fmt.Sprintf("%v", v[0]): v[2],
					},
				})
			case ">": //范围查询 ['name','>','2']
				filter = append(filter, map[string]interface{}{
					"range": map[string]interface{}{
						fmt.Sprintf("%v", v[0]): map[string]interface{}{
							"gt": v[2],
						},
					},
				})
			case ">=": //范围查询 ['name','>=','2']
				filter = append(filter, map[string]interface{}{
					"range": map[string]interface{}{
						fmt.Sprintf("%v", v[0]): map[string]interface{}{
							"gte": v[2],
						},
					},
				})
			case "<": //范围查询 ['name','<','2']
				filter = append(filter, map[string]interface{}{
					"range": map[string]interface{}{
						fmt.Sprintf("%v", v[0]): map[string]interface{}{
							"lt": v[2],
						},
					},
				})
			case "<=": //范围查询 ['name','<=','2']
				filter = append(filter, map[string]interface{}{
					"range": map[string]interface{}{
						fmt.Sprintf("%v", v[0]): map[string]interface{}{
							"lte": v[2],
						},
					},
				})
			case "range": //范围查询 ['name','range',['gte'=>2020,'lte'=>2021]]
				filter = append(filter, map[string]interface{}{
					"range": map[string]interface{}{
						fmt.Sprintf("%v", v[0]): v[2],
					},
				})
			case "like": //模糊查询 ['name','like','*张三*']
				must = append(must, map[string]interface{}{
					"wildcard": map[string]interface{}{
						fmt.Sprintf("%v", v[0]): v[2],
					},
				})
			case "not like": //模糊查询 ['name','not like','*张三*']
				must_not = append(must_not, map[string]interface{}{
					"wildcard": map[string]interface{}{
						fmt.Sprintf("%v", v[0]): v[2],
					},
				})
			}
		}
	}

	e.Query = map[string]map[string]interface{}{
		"bool": {
			"filter":   filter,
			"must":     must,
			"must_not": must_not,
		},
	}
	return e
}

/**
* 查询字段
* @param array field 是一个多维关联数组:['id',...]
 */
func (e *Esconfig) Field(field []string) *Esconfig {
	e.Fields = field
	return e
}

/**
* 分页
* @param int from 分页起始值
* @param int size 最大数不能大于1W
 */
func (e *Esconfig) Limit(from, size int64) *Esconfig {
	e.From = from
	e.Size = size
	return e
}

/**
* 排序
* @param array param 是一个多维关联数组:[['id'=>'desc']]
 */
func (e *Esconfig) Order(param []map[string]string) *Esconfig {
	e.Sort = param
	return e
}

// 多条查询查询订单信息
func (e *Esconfig) Select(any interface{}) error {
	if e.Size == 0 {
		e.Size = 10000
	}

	//查询订单信息
	query := map[string]interface{}{
		"query":   e.Query,
		"_source": e.Fields,
		"from":    e.From,
		"size":    e.Size,
	}

	// 排序字段处理
	if len(e.Sort) > 0 {
		var is_sort bool
		sort := make(map[string]string)
		for _, v := range e.Sort {
			for key, val := range v {
				is_sort = true
				sort[key] = val
			}
		}
		if is_sort {
			query["sort"] = sort
		}
	}

	sql_byte, err := json.Marshal(query)
	if err != nil {
		return err
	}
	err = e.QueryBody(e.Index, string(sql_byte), &any)
	if err != nil {
		return err
	}

	return nil
}

// 查询订单信息
func (e *Esconfig) IDByUpdate(id string, params map[string]interface{}) error {
	updateRequest := e.Client.Update().
		Index(e.Prefix + e.Index).
		Id(id).
		Doc(params).
		DetectNoop(true)

	_, err := updateRequest.Do(context.Background())
	if err != nil {
		logger.I("ES更新文档失败", err)
		return err
	}

	return nil
}

// 查询订单信息
func (e *Esconfig) QueryEsOrderInfo(order_no *[]string, field *[]string, any interface{}) error {
	//查询订单信息
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": []map[string]interface{}{
					{
						"terms": map[string]interface{}{
							"_id": *order_no,
						},
					},
				},
			},
		},
		"_source": *field,
	}
	sql_byte, _ := json.Marshal(query)
	err := e.QueryBody("orders", string(sql_byte), &any)
	if err != nil {
		return xerr.NewErrMsg(fmt.Sprintf("订单数据查询失败，%s", err.Error()))
	}

	return nil
}

// 自定义查询
func (e *Esconfig) QueryBody(index, sql string, any interface{}) error {
	var data []map[string]interface{}
	// sreq := elastic.NewSearchRequest().Source(`{"query":{"match_all":{}},"_source":"sub_order_no"}`)
	sreq := elastic.NewSearchRequest().Source(sql)
	searchResult, err := e.Client.MultiSearch().
		Index(e.Prefix + index).
		Add(sreq).
		Do(context.TODO())
	if err != nil {
		logger.E("Es QueryBody Error", fmt.Sprintf("source：%s，Error：%s", sql, err.Error()))
		return err
	}
	if searchResult.Responses == nil {
		logger.E("Es QueryBody Error", fmt.Sprintf("source：%s，expected responses != nil; got nil", sql))
		return err
	}
	sres := searchResult.Responses[0]
	if sres == nil {
		logger.E("Es QueryBody Error", fmt.Sprintf("source：%s，expected sres != nil; got nil", sql))
		return err
	}
	if sres.Hits == nil {
		logger.E("Es QueryBody Error", fmt.Sprintf("source：%s，expected Hits != nil; got nil", sql))
		return err
	}
	for _, hit := range sres.Hits.Hits {
		item := make(map[string]interface{})
		json.Unmarshal(hit.Source, &item)
		data = append(data, item)
	}

	resp, _ := json.Marshal(data)
	err = json.Unmarshal(resp, any)

	return err
}

// 自定义查询返回数据及条数
func (e *Esconfig) CustomQuery(index, sql string) (resp *[]map[string]interface{}, count int64, err error) {
	var data []map[string]interface{}
	sreq := elastic.NewSearchRequest().Index(e.Prefix + index).Source(sql).Size(0)
	searchResult, err := e.Client.MultiSearch().
		Add(sreq).
		Do(context.TODO())
	if err != nil {
		logger.E("Es QueryBody Error", fmt.Sprintf("source：%s，Error：%s", sql, err.Error()))
		return resp, count, err
	}
	if searchResult.Responses == nil {
		logger.E("Es QueryBody Error", fmt.Sprintf("source：%s，expected responses != nil; got nil", sql))
		return resp, count, err
	}
	sres := searchResult.Responses[0]
	if sres == nil {
		logger.E("Es QueryBody Error", fmt.Sprintf("source：%s，expected sres != nil; got nil", sql))
		return resp, count, err
	}
	if sres.Hits == nil {
		logger.E("Es QueryBody Error", fmt.Sprintf("source：%s，expected Hits != nil; got nil", sql))
		return resp, count, err
	}

	count = sres.Hits.TotalHits.Value
	for _, hit := range sres.Hits.Hits {
		item := make(map[string]interface{})
		json.Unmarshal(hit.Source, &item)
		data = append(data, item)
	}

	return &data, count, nil
}
