package main

import (
	cf "engine/common/config"
	_ "engine/common/logger"
	"fmt"

	"engine/api/command"
	"engine/api/internal/config"
	"engine/api/internal/handler"
	"engine/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// 商品微服务
func main() {

	c := config.NewConfig()
	cf.InitApiConfig(c, "go-common-config", "vinehoo.conf", 0)

	server := rest.MustNewServer(c.RestConf)
	defer server.Stop()

	ctx := svc.NewServiceContext(*c)
	handler.RegisterHandlers(server, ctx)

	command.NewTimedTasksLogic(ctx)

	fmt.Printf("Starting server at %s:%d...\n", c.Host, c.Port)
	server.Start()
}
