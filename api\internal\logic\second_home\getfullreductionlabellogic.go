package second_home

import (
	"context"

	"engine/api/internal/service/fullreductionlabel"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetFullReductionLabelLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetFullReductionLabelLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetFullReductionLabelLogic {
	return &GetFullReductionLabelLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetFullReductionLabelLogic) GetFullReductionLabel(req *types.GetFullReductionLabelReq) (resp *types.GetFullReductionLabelResp, err error) {
	var periods []int64
	result := types.GetFullReductionLabelResp{
		List: make(map[int64][]types.ProductLabelTypes),
	}
	if len(req.Data) == 0 {
		return &result, xerr.NewParamErrMsg("查询期数不能为空")
	}
	//所有期数
	for _, v := range req.Data {
		periods = append(periods, v.Periods)
	}
	//期数分类
	periods_class := l.GetPeriodsClass(req)
	//查询满减标签
	period_data := fullreductionlabel.NewFullreductionlabelLogic(l.svcCtx).GetLabel(&periods, periods_class)
	period_info := *LabelSorting(period_data)
	for _, v := range periods {
		period_label := make([]types.ProductLabelTypes, 0)
		if vv, ok := period_info[v]; ok {
			period_label = append(period_label, vv...)
		}
		result.List[v] = period_label
	}

	return &result, nil
}

// 获取商品分类
func (l *GetFullReductionLabelLogic) GetPeriodsClass(req *types.GetFullReductionLabelReq) *[][]int64 {
	var (
		periods_class PeriodsClass
		period_info   [][]int64
	)
	for _, v := range req.Data {
		switch v.PeriodsType {
		case 0: //闪购
			periods_class.FlashPeriod = append(periods_class.FlashPeriod, v.Periods)
		case 1: //秒发
			periods_class.SecondPeriod = append(periods_class.SecondPeriod, v.Periods)
		case 2: //跨境
			periods_class.CrossPeriod = append(periods_class.CrossPeriod, v.Periods)
		case 3: //尾货
			periods_class.LeftoverPeriod = append(periods_class.LeftoverPeriod, v.Periods)
		case 9: //秒发商家
			periods_class.SecondMerchantsPeriod = append(periods_class.SecondMerchantsPeriod, v.Periods)
		}
	}

	period_info = append(period_info,
		periods_class.FlashPeriod,           //闪购
		periods_class.SecondPeriod,          //秒发
		periods_class.CrossPeriod,           //跨境
		periods_class.LeftoverPeriod,        //尾货
		periods_class.SecondMerchantsPeriod, //秒发商家
	)
	return &period_info
}
