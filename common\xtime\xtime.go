package xtime

import (
	"fmt"
	"strconv"
	"strings"
	"time"
)

// 获取当日开始时间（时间、时间戳）
func GetToDayDateTime() (string, int64) {
	d := time.Unix(time.Now().Unix(), 0)
	//获取当前时区
	loc, _ := time.LoadLocation("Local")

	//获取当日开始日期
	sDate := d.Format("2006-01-02") + " 00:00:00"
	sTime, _ := time.ParseInLocation("2006-01-02 15:04:05", sDate, loc)

	return sDate, sTime.Unix()
}

// 计算繁忙度当日开始时间、结束时间
func GetBusinessTime() (string, string) {
	d := time.Unix(time.Now().Unix()-60, 0)

	//开始日期
	startTime := d.Format("2006-01-02 15:04") + ":00"
	//结束时间日期
	endTime := d.Format("2006-01-02 15:04") + ":59"

	return startTime, endTime
}

// 日期转时间戳
func StrToTime(date string) int64 {
	//获取当前时区
	loc, _ := time.LoadLocation("Local")
	times, _ := time.ParseInLocation("2006-01-02 15:04:05", date, loc)

	return times.Unix()
}

// 日期转时间戳
func StrToTimeFormat(format, date string) int64 {
	//获取当前时区
	loc, _ := time.LoadLocation("Local")
	times, _ := time.ParseInLocation(format, date, loc)

	return times.Unix()
}

// 时间转时间戳
func TimeToTime(d time.Time) int64 {
	date := d.Format("2006-01-02 15:04:05")
	//获取当前时区
	loc, _ := time.LoadLocation("Local")
	times, _ := time.ParseInLocation("2006-01-02 15:04:05", date, loc)

	return times.Unix()
}

// 时间转日期
func TimeToDate(d time.Time) string {
	date := d.Format("2006-01-02 15:04:05")

	return date
}

// 时间戳转日期
func Date(timestamp int64) string {
	// 根据时间戳创建时间对象
	t := time.Unix(timestamp, 0)

	// 格式化时间为字符串
	timeString := t.Format("2006-01-02 15:04:05")
	return timeString
}

// 时间戳转日期
func DateFormat(format string, timestamp int64) string {
	// 根据时间戳创建时间对象
	t := time.Unix(timestamp, 0)

	// 格式化时间为字符串
	timeString := t.Format(format)
	return timeString
}

// 获取今日0点时间戳
func TodayTimeStamp() int64 {
	// 获取当前时间
	now := time.Now()

	// 获取当天的日期
	year, month, day := now.Date()

	// 构建今天零点的时间
	zeroTime := time.Date(year, month, day, 0, 0, 0, 0, time.Local)

	// 获取零点时间的时间戳
	zeroTimestamp := zeroTime.Unix()
	return zeroTimestamp
}

// 获取今日23点59分59秒时间戳
func TodayEndTimeStamp() int64 {
	// 获取当前时间
	now := time.Now()

	// 获取当天的日期
	year, month, day := now.Date()

	// 构建今天23点的时间
	endTime := time.Date(year, month, day, 23, 59, 59, 0, time.Local)

	// 获取23点的时间戳
	endTimestamp := endTime.Unix()
	return endTimestamp
}

// 秒数转年月日时分秒
func SecTime(second int64) string {
	var (
		years   time.Duration
		months  time.Duration
		days    time.Duration
		hours   time.Duration
		minutes time.Duration
		result  string
	)
	duration := time.Duration(second) * time.Second
	//年
	if duration > (365 * 24 * time.Hour) {
		years = duration / (365 * 24 * time.Hour)
		duration -= years * 365 * 24 * time.Hour
		result = fmt.Sprintf("%d年", years)
	}
	//月
	if duration > (30 * 24 * time.Hour) {
		months = duration / (30 * 24 * time.Hour)
		duration -= months * 30 * 24 * time.Hour
		result = fmt.Sprintf("%s%d月", result, months)
	}
	//日
	if duration > (24 * time.Hour) {
		days = duration / (24 * time.Hour)
		duration -= days * 24 * time.Hour
		result = fmt.Sprintf("%s%d天", result, days)
	}
	//时
	if duration > time.Hour {
		hours = duration / time.Hour
		duration -= hours * time.Hour
		result = fmt.Sprintf("%s%d小时", result, hours)
	}
	//分
	if duration > time.Minute {
		minutes = duration / time.Minute
		duration -= minutes * time.Minute
		result = fmt.Sprintf("%s%d分钟", result, minutes)
	}
	//秒
	seconds := int(duration / time.Second)

	return fmt.Sprintf("%s%d秒", result, seconds)
}

// 获取两个时间戳中间值
func SecTimeMedian(stime int64, etime int64) int64 {
	// 转换为time.Time类型
	startTime := time.Unix(stime, 0)
	endTime := time.Unix(etime, 0)

	// 计算中间时间戳
	midTime := startTime.Add(endTime.Sub(startTime) / 2)

	return midTime.Unix()
}

// 获取两个时间戳之间所有分钟
func SecTimeBefore(stime int64, etime int64) []string {
	startTime := time.Unix(stime, 0)
	endTime := time.Unix(etime, 0)

	// Loop through minutes and generate formatted date strings
	var date []string
	currentTime := startTime
	for currentTime.Before(endTime) {
		date = append(date, currentTime.Format("2006-01-02 15:04"))
		currentTime = currentTime.Add(time.Minute)
	}
	return date
}

// 获取当前月开始时间戳和结束时间戳
func GetMonthStartAndEndTimestamp(input string) (int64, int64) {
	parts := strings.Split(input, "-")
	if len(parts) != 2 {
		return 0, 0
	}
	// 将年份和月份解析为整数
	year, err1 := strconv.Atoi(parts[0])
	month, err2 := strconv.Atoi(parts[1])

	if err1 != nil || err2 != nil {
		return 0, 0
	}
	//获取当前时区
	loc, _ := time.LoadLocation("Local")

	// 构建指定年月的第一天的时间
	startTime := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, loc)

	// 下个月的第一天减一秒即为本月的最后一秒
	endTime := startTime.AddDate(0, 1, 0).Add(-time.Second)

	// 转换为时间戳
	startTimestamp := startTime.Unix()
	endTimestamp := endTime.Unix()

	return startTimestamp, endTimestamp
}

// 获取指定月份所有日志
func GetMonthAllDate(inputDate string) []string {
	result := make([]string, 0)
	// 将字符串解析为时间类型
	t, err := time.Parse("2006-01", inputDate)
	if err != nil {
		return result
	}

	startOfMonth := time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, t.Location())
	endOfMonth := time.Date(t.Year(), t.Month()+1, 0, 0, 0, 0, 0, t.Location())
	for !startOfMonth.After(endOfMonth) {
		if startOfMonth.Unix() < time.Now().Unix() {
			result = append(result, startOfMonth.Format("2006-01-02"))
		}
		startOfMonth = startOfMonth.AddDate(0, 0, 1)
	}

	return result
}

// 获取指定日期星期几
func GetDateweek(inputDate string) string {
	var result string
	t, err := time.Parse("2006-01-02", inputDate)
	if err != nil {
		return result
	}
	weekday := t.Weekday()

	// 打印星期几的名称
	switch weekday {
	case time.Monday:
		result = "星期一"
	case time.Tuesday:
		result = "星期二"
	case time.Wednesday:
		result = "星期三"
	case time.Thursday:
		result = "星期四"
	case time.Friday:
		result = "星期五"
	case time.Saturday:
		result = "星期六"
	case time.Sunday:
		result = "星期日"
	}
	return result
}

// 获取日期的开始时间戳和结束时间戳
func GetDateStartAndEnd(inputDate string) (int64, int64) {
	var (
		stime int64
		etime int64
	)
	t, err := time.Parse("2006-01-02", inputDate)
	if err != nil {
		return stime, etime
	}

	stime = StrToTime(t.Format("2006-01-02") + " 00:00:00")
	etime = StrToTime(t.Format("2006-01-02") + " 23:59:59")

	return stime, etime
}

// date日期修改为时间戳的日期
func FormatTime(date string, timestamp int64) string {
	// 将字符串解析为时间
	d, _ := time.Parse("2006-01-02 15:04:05", date)

	// 获取当前时区
	loc, _ := time.LoadLocation("Local")

	// 解析为本地时间
	t := d.In(loc)

	// 格式化为指定格式的字符串
	timeString := t.Format("15:04:05")

	// 格式化时间戳为日期字符串
	t = time.Unix(timestamp, 0)
	dateString := t.Format("2006-01-02")

	return dateString + " " + timeString
}

// 获取多少天前0点时间戳
func GetDayStartTimestamp(day int) int64 {
	// 获取当前时间
	now := time.Now()

	// 获取当前时间的零点时间
	zeroTime := time.Date(now.Year(), now.Month(), now.Day()-day, 0, 0, 0, 0, now.Location())

	return zeroTime.Unix()
}
