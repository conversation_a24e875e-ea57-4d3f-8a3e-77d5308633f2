package data_analysis

import "time"

type CommoditiesPeriods struct {
	Id          int64
	OnsaleTime  int64
	SellTime    int64
	SoldOutTime int64
	IsChannel   int64
	IssueType   int64
}

type OrdersData struct {
	Id            int64
	PaymentAmount float64
	CreatedTime   int64
	Period        int64
}

type GetSaleBottleNumsResp struct {
	Data map[string]interface{} `json:"data"`
}

type GoodsGetFictitiousCountResp struct {
	Data map[string][]GoodsGetFictitiousCountRespInfo `json:"data"`
}

type GoodsGetFictitiousCountRespInfo struct {
	FictitiousId int64  `json:"fictitious_id"`
	BarCode      string `json:"bar_code"`
	GoodsCount   int64  `json:"goods_count"`
	TransitCount int64  `json:"transit_count"`
}

type Supplier struct {
	ContractEnd  time.Time
	SupplierTax  string
	SupplierName string
}

type Storage struct {
	StorageId       int64 `gorm:"primaryKey"`
	WarehousingCode string
	RdCode          string
	Status          int64
}

type TailInventoryWms struct {
	Id                     int64 `gorm:"primaryKey"`
	CnGoodsName            string
	EnGoodsName            string
	ShortCode              string
	BarCode                string
	Inventory              string
	IsSale                 int64
	Period                 string
	PeriodInfo             string
	RemainingSaleInventory string
	CreatedTime            int64
}
