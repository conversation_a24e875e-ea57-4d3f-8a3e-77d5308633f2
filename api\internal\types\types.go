// Code generated by goctl. DO NOT EDIT.
package types

type GetProductLabelReq struct {
	IsNewUser string                   `json:"is_new_user,optional" validate:"omitempty" v:"是否新用户"`
	Data      []GetProductLabelReqData `json:"data" validate:"required" v:"期数数据"`
}

type GetProductLabelReqData struct {
	Periods     int64 `json:"periods" validate:"required,min=1" v:"期数"`
	PeriodsType int64 `json:"periods_type" validate:"required,min=0" v:"期数类型"`
}

type GetProductLabelResp struct {
	List map[int64]GetProductLabelRespList `json:"list"`
}

type GetProductLabelRespList struct {
	ProductLabel []ProductLabelTypes `json:"product_label"`
	TopLabel     []ProductLabelTypes `json:"top_label"`
	LeftTopLabel []ProductLabelTypes `json:"left_top_label"`
}

type ProductLabelTypes struct {
	Id    int64  `json:"id"`
	Type  int64  `json:"type"`
	Title string `json:"title"`
}

type GetFullReductionLabelReq struct {
	Data []GetProductLabelReqData `json:"data" validate:"required" v:"期数数据"`
}

type GetFullReductionLabelResp struct {
	List map[int64][]ProductLabelTypes `json:"list"`
}

type AggregationReq struct {
	Client    int64  `form:"client" validate:"omitempty,min=0" v:"广告客户端"`
	Mid       string `form:"mid,optional" validate:"omitempty" v:"商家ID"`
	Longitude string `form:"longitude,optional" validate:"omitempty" v:"经度"`
	Latitude  string `form:"latitude,optional" validate:"omitempty" v:"纬度"`
}

type AggregationResp struct {
	HomeScreen        []map[string]interface{} `json:"home_screen"`
	CollectTags       map[string]interface{}   `json:"collect_tags"`
	HotTopic          []map[string]interface{} `json:"hot_topic"`
	Banner            []map[string]interface{} `json:"banner"`
	NewusersActivity  map[string]interface{}   `json:"newusers_activity"`
	SecondFiltersList []map[string]interface{} `json:"second_filters_list"`
}

type GetContentReq struct {
	Page     int64  `form:"page" validate:"required,min=1" v:"当前页"`
	Keywords string `form:"keywords" validate:"required" v:"关键字"`
}

type GetContentResp struct {
	Total int64                `json:"total"`
	List  []GetContentRespList `json:"list"`
}

type GetContentRespList struct {
	Genre string                 `json:"genre"`
	Post  map[string]interface{} `json:"post"`
	News  map[string]interface{} `json:"news"`
	Wine  map[string]interface{} `json:"wine"`
}

type AggregationV2Req struct {
	Client    int64  `form:"client" validate:"omitempty,min=0" v:"广告客户端"`
	Mid       string `form:"mid,optional" validate:"omitempty" v:"商家ID"`
	Longitude string `form:"longitude,optional" validate:"omitempty" v:"经度"`
	Latitude  string `form:"latitude,optional" validate:"omitempty" v:"纬度"`
	Uuid      string `form:"mid,optional" validate:"omitempty" v:"uuid"`
}

type AggregationV2Resp struct {
	Column []map[string]interface{} `json:"column"`
	Card   []map[string]interface{} `json:"card"`
}

type FiltersListReq struct {
	Page  int64  `form:"page" validate:"required,min=1" v:"当前页"`
	Limit int64  `form:"limit" validate:"required,min=5" v:"返回条数"`
	Type  string `form:"type,optional" validate:"omitempty" v:"标签类型"`
}

type FiltersListResp struct {
	Total int64                  `json:"total"`
	List  []PeriodsSecondFilters `json:"list"`
}

type PeriodsSecondFilters struct {
	Id          int64  `json:"id"`
	FiltersType int64  `json:"filters_type"`
	Status      int64  `json:"status"`
	Title       string `json:"title"`
	Identifier  string `json:"identifier"`
	WeightValue int64  `json:"weight_value"`
	Operator    string `json:"operator"`
	UpdateTime  string `json:"update_time"`
}

type AddFiltersReq struct {
	Title       string `json:"title" validate:"required" v:"标题"`
	WeightValue int64  `json:"weight_value" validate:"omitempty" v:"权重值"`
}

type EditFiltersReq struct {
	Id          int64  `json:"id" validate:"required,min=1" v:"ID"`
	Status      int64  `json:"status" validate:"omitempty" v:"状态"`
	Title       string `json:"title" validate:"required" v:"标题"`
	WeightValue int64  `json:"weight_value" validate:"omitempty" v:"权重值"`
}

type UpdateFiltersReq struct {
	Id     int64 `json:"id" validate:"required,min=1" v:"ID"`
	Status int64 `json:"status" validate:"omitempty" v:"状态"`
}

type FiltersGoodsListReq struct {
	Page      int64  `form:"page" validate:"required,min=1" v:"当前页"`
	Limit     int64  `form:"limit" validate:"required,min=5" v:"返回条数"`
	FiltersId int64  `form:"filters_id" validate:"required,min=1" v:"筛选标签id"`
	Periods   string `form:"periods,optional" validate:"omitempty" v:"期数"`
	Status    string `form:"status,optional" validate:"omitempty" v:"状态"`
}

type FiltersGoodsListResp struct {
	Total int64                  `json:"total"`
	List  []FiltersGoodsListData `json:"list"`
}

type FiltersGoodsListData struct {
	Id             int64   `json:"id"`
	FiltersId      int64   `json:"filters_id"`
	Periods        int64   `json:"periods"`
	PeriodsType    int64   `json:"periods_type"`
	GoodsName      string  `json:"goods_name"`
	GoodsShortName string  `json:"goods_short_name"`
	Images         string  `json:"images"`
	UnitPrice      float64 `json:"unit_price"`
	MarketPrice    float64 `json:"market_price"`
	Status         int64   `json:"status"`
	WeightValue    int64   `json:"weight_value"`
	UpdateName     string  `json:"update_name"`
	UpdateTime     string  `json:"update_time"`
}

type AddFiltersGoodsReq struct {
	FiltersId      int64  `json:"filters_id" validate:"required,min=1" v:"筛选标签id"`
	Periods        int64  `json:"periods" validate:"required,min=1" v:"期数"`
	PeriodsType    int64  `json:"periods_type" validate:"omitempty" v:"期数频道"`
	GoodsShortName string `json:"goods_short_name,optional" validate:"omitempty" v:"商品简称"`
	Status         int64  `json:"status" validate:"omitempty" v:"状态"`
	WeightValue    int64  `json:"weight_value" validate:"omitempty" v:"权重值"`
}

type EditFiltersGoodsReq struct {
	Id             int64  `json:"id" validate:"required,min=1" v:"商品记录id"`
	FiltersId      int64  `json:"filters_id" validate:"required,min=1" v:"筛选标签id"`
	Periods        int64  `json:"periods" validate:"required,min=1" v:"期数"`
	PeriodsType    int64  `json:"periods_type" validate:"omitempty" v:"期数频道"`
	GoodsShortName string `json:"goods_short_name,optional" validate:"omitempty" v:"商品简称"`
	Status         int64  `json:"status" validate:"omitempty" v:"状态"`
	WeightValue    int64  `json:"weight_value" validate:"omitempty" v:"权重值"`
}

type UpdateFiltersGoodsReq struct {
	Id            int64 `json:"id" validate:"required,min=1" v:"商品记录id"`
	OperationType int64 `json:"operation_type" validate:"omitempty" v:"操作类型"`
	Value         int64 `json:"value" validate:"omitempty" v:"操作值"`
}

type DeleteFiltersGoodsReq struct {
	Id int64 `json:"id" validate:"required,min=1" v:"商品记录id"`
}

type GetFiltersListReq struct {
	Longitude string `form:"longitude,optional" validate:"omitempty" v:"经度"`
	Latitude  string `form:"latitude,optional" validate:"omitempty" v:"纬度"`
}

type GetFiltersListResp struct {
	List []GetFiltersListData `json:"list"`
}

type GetFiltersListData struct {
	Title      string `json:"title"`
	Identifier string `json:"identifier"`
}

type OnlineGoodsRealtimeStatisticsReq struct {
	CountKey string `form:"count_key" validate:"required" v:"统计类型"`
	Month    string `form:"month" validate:"required" v:"月份"`
}

type OnlineGoodsRealtimeStatisticsResp struct {
	Date                  string  `json:"date"`
	Week                  string  `json:"week"`
	FlashNews             int64   `json:"flash_news"`
	FlashSale             int64   `json:"flash_sale"`
	FlashSales            float64 `json:"flash_sales"`
	FlashOrders           int64   `json:"flash_orders"`
	SecondNews            int64   `json:"second_news"`
	SecondSale            int64   `json:"second_sale"`
	SecondSales           float64 `json:"second_sales"`
	SecondOrders          int64   `json:"second_orders"`
	CrossNews             int64   `json:"cross_news"`
	CrossSale             int64   `json:"cross_sale"`
	CrossSales            float64 `json:"cross_sales"`
	CrossOrders           int64   `json:"cross_orders"`
	TailNews              int64   `json:"tail_news"`
	TailSale              int64   `json:"tail_sale"`
	TailSales             float64 `json:"tail_sales"`
	TailOrders            int64   `json:"tail_orders"`
	ChannelNews           int64   `json:"channel_news"`
	ChannelSale           float64 `json:"channel_sale"`
	ChannelSales          float64 `json:"channel_sales"`
	ChannelOrders         int64   `json:"channel_orders"`
	AuctionNews           int64   `json:"auction_news"`
	AuctionSale           float64 `json:"auction_sale"`
	AuctionSales          float64 `json:"auction_sales"`
	AuctionOrders         int64   `json:"auction_orders"`
	PersonalAuctionNews   int64   `json:"personal_auction_news"`
	PersonalAuctionSale   float64 `json:"personal_auction_sale"`
	PersonalAuctionSales  float64 `json:"personal_auction_sales"`
	PersonalAuctionOrders int64   `json:"personal_auction_orders"`
}

type ProductSaleFollowDatasReq struct {
	MainPeriod []int64  `json:"main_period" validate:"required" v:"最后一期期数"`
	ShortCode  []string `json:"short_code" validate:"required" v:"简码"`
	PeriodId   []int64  `json:"period_id" validate:"required" v:"所有期数"`
	Type       int64    `json:"type,optional" validate:"omitempty" v:"查询类型：0-所有，1-产品信息"`
}

type ProductSaleFollowDatasResp struct {
	Products        map[string]ProductSaleFollowProducts       `json:"products"`
	Packages        map[int64][]ProductSaleFollowPackages      `json:"packages"`
	Periods         map[int64]ProductSaleFollowPeriodsPeriods  `json:"periods"`
	SaleNum         map[string]interface{}                     `json:"sale_num"`
	MyInventoryData map[string]int64                           `json:"my_inventory_data"`
	PeriodsProduct  map[string]ProductSaleFollowPeriodsProduct `json:"periods_product"`
	StorageGoods    map[string]ProductSaleFollowStorageGoods   `json:"storage_goods"`
	ThreeDaySaleNum map[string]interface{}                     `json:"threeday_sale_num"`
}

type ProductSaleFollowStorageGoods struct {
	BarCode    string `json:"bar_code"`
	ShortCode  string `json:"short_code"`
	UpdateTime int64  `json:"update_time"`
}

type ProductSaleFollowPeriodsPeriods struct {
	Id            int64  `json:"id"`
	OnsaleStatus  int64  `json:"onsale_status"`
	PeriodsType   int64  `json:"periods_type"`
	ImportType    int64  `json:"import_type"`
	Supplier      string `json:"supplier"`
	SupplierId    int64  `json:"supplier_id"`
	BuyerName     string `json:"buyer_name"`
	BuyerId       int64  `json:"buyer_id"`
	OperationName string `json:"operation_name"`
	OperationId   int64  `json:"operation_id"`
	IsChannel     int64  `json:"is_channel"`
}

type ProductSaleFollowPeriodsProduct struct {
	Id        int64   `json:"id"`
	Period    int64   `json:"period"`
	ProductId int64   `json:"product_id"`
	BarCode   string  `json:"bar_code"`
	ShortCode string  `json:"short_code"`
	Costprice float64 `json:"costprice"`
}

type ProductSaleFollowProducts struct {
	Id              int64  `json:"id"`
	ShortCode       string `json:"short_code"`
	BarCode         string `json:"bar_code"`
	CnProductName   string `json:"cn_product_name"`
	EnProductName   string `json:"en_product_name"`
	CountryNameCn   string `json:"country_name_cn"`
	WineryNameCn    string `json:"winery_name_cn"`
	ProductTypeName string `json:"product_type_name"`
}

type ProductSaleFollowPackages struct {
	Id                 int64   `json:"id"`
	PeriodId           int64   `json:"period_id"`
	PackageName        string  `json:"package_name"`
	Price              float64 `json:"price"`
	AssociatedProducts string  `json:"associated_products"`
}

type GetPurchaseOrdersRealTimeDataReq struct {
	PurchaseOrderNo []string `json:"purchase_order_no,optional" validate:"omitempty" v:"采购单号"`
	PeriodId        []int64  `json:"period_id" validate:"required" v:"所有期数"`
	ShortCode       []string `json:"short_code" validate:"required" v:"简码"`
	SupplierName    []string `json:"supplier_name" validate:"required" v:"供应商"`
	QueryType       int64    `json:"query_type,optional" validate:"omitempty" v:"查询类型"`
}

type GetPurchaseOrdersRealTimeDataResp struct {
	Periods       map[int64]map[string]interface{}  `json:"periods"`
	Products      map[string]map[string]interface{} `json:"products"`
	Supplier      map[string]map[string]interface{} `json:"supplier"`
	PeriodProduct map[string]map[string]interface{} `json:"period_product"`
	GrossMargin   map[string]interface{}            `json:"gross_margin"`
	SaleNum       map[string]interface{}            `json:"sale_num"`
	UnshippedNum  map[string]interface{}            `json:"unshipped_num"`
	MyInventory   map[string]map[string]interface{} `json:"my_inventory"`
	Mystorage     map[string]map[string]interface{} `json:"my_storage"`
}

type GetTailInventoryReq struct {
	Page      int64  `form:"page" validate:"required" v:"当前页"`
	Limit     int64  `form:"limit" validate:"required" v:"返回条数"`
	ShortCode string `form:"short_code,optional" validate:"omitempty" v:"简码"`
	NotSale   int64  `form:"not_sale,optional" validate:"omitempty" v:"不在售"`
}

type GetTailInventoryResp struct {
	List  []GetTailInventoryRespList `json:"list"`
	Total int64                      `json:"total"`
}

type GetTailInventoryRespList struct {
	Id                     int64                    `json:"id"`
	CnGoodsName            string                   `json:"cn_goods_name"`
	EnGoodsName            string                   `json:"en_goods_name"`
	ShortCode              string                   `json:"short_code"`
	BarCode                string                   `json:"bar_code"`
	Inventory              []map[string]interface{} `json:"inventory"`
	IsSale                 int64                    `json:"is_sale"`
	Period                 string                   `json:"period"`
	PeriodInfo             []map[string]interface{} `json:"period_info"`
	RemainingSaleInventory []map[string]interface{} `json:"remaining_sale_inventory"`
	CreatedTime            string                   `json:"created_time"`
}

type NewInventoryNoticeReq struct {
	Type int64 `form:"type,optional" validate:"omitempty" v:"类型"`
}

type OneFlowerOneWorldKanbanReq struct {
	Page          int64  `form:"page,optional" validate:"omitempty" v:"当前页"`
	Limit         int64  `form:"limit,optional" validate:"omitempty" v:"返回条数"`
	ShortCode     string `form:"short_code,optional" validate:"omitempty" v:"简码"`
	WarehouseCode string `form:"warehouse_code,optional" validate:"omitempty" v:"仓库编码"`
	Type          int64  `form:"type,optional,default=1" validate:"omitempty" v:"类型:1=汇总,2=销售明细,3=采购明细"`
}

type OneFlowerOneWorldKanbanResp struct {
	List  []OneFlowerOneWorldKanbanRespList `json:"list"`
	Total int64                             `json:"total"`
}

type OneFlowerOneWorldKanbanRespList struct {
	Warehouse        string `json:"warehouse"`
	WarehouseCode    string `json:"warehouse_code"`
	ShortCode        string `json:"short_code"`
	CnGoodsName      string `json:"cn_goods_name"`
	Capacity         string `json:"capacity"`
	Unit             string `json:"unit"`
	PurchaseNum      int64  `json:"purchase_num"`
	SaleNum          int64  `json:"sale_num"`
	AllocationOutNum int64  `json:"allocation_out_num"`
	AllocationInNum  int64  `json:"allocation_in_num"`
	InventoryNum     int64  `json:"inventory_num"`
}

type GetFullReductionActivityLabelReq struct {
	Data []GetFullReductionActivityLabelReqData `json:"data" validate:"required" v:"数据"`
}

type GetFullReductionActivityLabelReqData struct {
	Period     int64 `json:"period" validate:"required" v:"期数"`
	PeriodType int64 `json:"period_type,optional" validate:"omitempty" v:"频道"`
}

type GetFullReductionActivityLabelResp struct {
	Label    map[int64][]string                 `json:"label"`
	Activity map[int64][]map[string]interface{} `json:"activity"`
}

type AutomaticallyAddPeriodReq struct {
	Period       int64   `json:"period" validate:"required" v:"期数"`
	PeriodType   int64   `json:"period_type,optional" validate:"omitempty" v:"频道"`
	Title        string  `json:"title,optional" validate:"omitempty" v:"标题"`
	Card         []int64 `json:"card,optional" validate:"omitempty" v:"卡片ID"`
	CardFilter   []int64 `json:"card_filter,optional" validate:"omitempty" v:"卡片筛选项ID"`
	Column       []int64 `json:"column,optional" validate:"omitempty" v:"栏目ID"`
	ColumnFilter []int64 `json:"column_filter,optional" validate:"omitempty" v:"栏目筛选项ID"`
	Label        []int64 `json:"label,optional" validate:"omitempty" v:"标签ID"`
	Inventory    int64   `json:"inventory,optional" validate:"omitempty" v:"是否操作库存"`
}

type GetMarketingSectionReq struct {
	Period     int64 `json:"period" validate:"required" v:"期数"`
	PeriodType int64 `json:"period_type,optional" validate:"omitempty" v:"频道"`
}

type GetMarketingSectionResp struct {
	Card   []MarketingSection `json:"card"`
	Column []MarketingSection `json:"column"`
}

type MarketingSection struct {
	Id     int64                    `json:"id"`
	Name   string                   `json:"name"`
	Filter []MarketingSectionFilter `json:"filter"`
}

type MarketingSectionFilter struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
}

type SaleGoodsByRuleAddLabelReq struct {
	LabelId int64 `json:"label_id" validate:"required" v:"标签id"`
}

type AutomaticallyAddAllPeriodReq struct {
	Card   []int64 `json:"card,optional" validate:"omitempty" v:"卡片ID"`
	Column []int64 `json:"column,optional" validate:"omitempty" v:"栏目ID"`
	Label  []int64 `json:"label,optional" validate:"omitempty" v:"标签ID"`
}
