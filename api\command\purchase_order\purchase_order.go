package purchase_order

import (
	"encoding/json"
	"engine/common"
	"engine/common/config"
	"engine/common/httpClient"
	"engine/common/logger"
	"engine/common/redigo"
	"engine/common/xtime"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/mr"
	"gorm.io/gorm"
)

var (
	Dtime          int64
	TodayTimestamp int64
	Workday        map[string]map[string]int64
)

/*采购下单*/
type PurchaseOrderLogic struct {
	Config     config.ApiConfig
	Db         *gorm.DB
	OrderDb    *gorm.DB
	WmsDb      *gorm.DB
	Rdb        *redigo.ClientConfig
	HttpClient *httpClient.HttpConfig
}

func NewPurchaseOrderLogicStruct(db, order_db, db_wms *gorm.DB, Config config.ApiConfig, rdb *redigo.ClientConfig) *PurchaseOrderLogic {
	return &PurchaseOrderLogic{
		Config:     Config,
		Db:         db,
		OrderDb:    order_db,
		WmsDb:      db_wms,
		Rdb:        rdb,
		HttpClient: httpClient.Handle(Config),
	}
}

func NewPurchaseOrderLogic(db, order_db, db_wms *gorm.DB, Config config.ApiConfig, rdb *redigo.ClientConfig) {
	l := &PurchaseOrderLogic{
		Config:     Config,
		Db:         db,
		OrderDb:    order_db,
		WmsDb:      db_wms,
		Rdb:        rdb,
		HttpClient: httpClient.Handle(Config),
	}
	// 运行
	go l.Run()
}

// 运行
func (l *PurchaseOrderLogic) Run() {
	for {
		timeN := time.Now()

		// 每天8点后执行
		hour := timeN.Hour()
		Minute := timeN.Minute()
		if hour == 8 && Minute == 0 {
			// 执行处理
			l.Execute()
		}

		fmt.Println("采购下单--ok")
		//1分钟执行一次
		time.Sleep(1 * time.Minute)
	}
}

// 执行处理
func (l *PurchaseOrderLogic) Execute() {
	var (
		periods []EsPeriodsInfo
		Workday map[string]int64
	)
	//当前时间戳
	Dtime = int64(time.Now().Unix())
	// 今日0点时间戳
	TodayTimestamp = xtime.TodayTimeStamp()

	fmt.Println("采购下单--start")
	defer fmt.Println("采购下单--end")

	mr.Finish(func() (err error) {
		// 获取支持采购下单的期数
		periods = l.GetPeriods()
		return
	}, func() (err error) {
		// 判断今日是否为工作日（包含调休在内需要上班的日子）
		Workday = l.getNextWorkday()
		return
	})
	if len(periods) == 0 {
		return
	}
	// 休息日处理，更新es采购时间为下一个工作日
	if Workday["workday"] == 2 {
		l.HolidayHandle(periods, Workday["next_workday"])
		return
	}

	// 创建采购单
	l.CreatePurchaseOrder(periods)
}

// 创建采购单
func (l *PurchaseOrderLogic) CreatePurchaseOrder(periods []EsPeriodsInfo) {
	var use_beihuo_data []UseBeiHuoData

	// 查询数据
	data := *l.QueryDatas(periods)

	// 采购单
	purchase_order := make(map[string]PurchaseOrderno)
	// 采购单产品
	purchase_order_item := make(map[string]map[string]PurchaseOrdernoItems)
	// 采购单期数产品
	period_items := make(map[string]map[string]PurchaseOrdernoPeriod)
	for _, period := range data.PeriodIds {
		var unit PartnerEntity
		// 今日已下单不再重复下单
		if _, ok := data.ExistPeriod[period]; ok {
			continue
		}
		// 期数信息
		if _, ok := data.PeriodInfoMap[period]; !ok {
			continue
		}
		prdinfo := data.PeriodInfoMap[period]
		// 订单信息
		if _, ok := data.OrdersMap[period]; !ok {
			continue
		}
		supplier_name := prdinfo.Supplier
		// 磐石供应商信息
		var supplier_info Supplier
		if s, ok := data.SupplierMap[prdinfo.SupplierId]; ok {
			supplier_info = s
			supplier_name = s.SupplierName
		}

		order_info := data.OrdersMap[period]
		// 往来单位
		if vv, ok := data.PartnerEntityMap[supplier_name]; ok {
			unit = vv
		}
		// 解析备注
		var unit_remarks UnitRemarks
		if unit.Remarks != "" {
			json.Unmarshal([]byte(unit.Remarks), &unit_remarks)
		}
		// 采购员信息
		var staff Staff
		if s, ok := data.StaffMap[prdinfo.BuyerName]; ok {
			staff = s
		}

		// 相同供应商一个采购单
		key := fmt.Sprintf("%s%d", supplier_name, prdinfo.PayeeMerchantId)
		if _, ok := purchase_order[key]; !ok {
			purchase_order[key] = PurchaseOrderno{
				Operator:          prdinfo.BuyerId,
				OperatorName:      prdinfo.BuyerName, //unit.Realname,
				CreatedTime:       Dtime,
				Period:            fmt.Sprintf("%d", prdinfo.Id),
				UpdateTime:        Dtime,
				Supplier:          supplier_name,
				SupplierCode:      unit.Code,
				OperatorCode:      staff.StaffCode,
				Department:        "产品中心", //staff.DeptName,
				DepartmentCode:    "01",   //staff.DeptCode,
				Setttlement:       "其他",
				SetttlementCode:   "03",
				Remark:            unit.Memo, //unit_remarks.Zqkz,
				CorpCode:          l.PayeeMerchantIdCodeExchange(prdinfo.PayeeMerchantId),
				PayeeMerchantId:   prdinfo.PayeeMerchantId,
				PayeeMerchantName: prdinfo.PayeeMerchantName,
				BillDate:          Dtime,
				Source:            2,
			}
			purchase_order_item[key] = make(map[string]PurchaseOrdernoItems)
			period_items[key] = make(map[string]PurchaseOrdernoPeriod)
		} else {
			order := purchase_order[key]
			// 期数
			order.Period = common.UniqueStrings(fmt.Sprintf("%s,%d", order.Period, period), ",")
			purchase_order[key] = order
		}

		for _, o := range order_info {
			// 套餐信息
			if _, ok := data.PackageMap[o.PackageId]; !ok {
				continue
			}
			pkg := data.PackageMap[o.PackageId]

			// 套餐产品
			for _, pdt := range pkg.Products {
				// 产品ID
				for _, pdtid := range pdt.ProductId {
					var (
						number         int64 // 下单数量
						sale_nums      int64 // 已售数量
						purchased_nums int64 // 已采数量
						is_first       int64 // 是否首次采购
					)
					//已售数量
					sale_nums = o.OrderQty * pdt.Nums
					// 采购单数据
					p_order := purchase_order[key]
					// 采购单产品数据
					p_order_item := purchase_order_item[key]
					// 采购单期数对应产品数据
					p_period_item := period_items[key]

					// 获取产品信息
					if vv, ok := data.ProductMap[fmt.Sprintf("%d%d", prdinfo.Id, pdtid)]; ok {
						// 仓库ID
						if p_order.Warehouse == "" {
							p_order.Warehouse = vv.Warehouse
							p_order.WarehouseCode = vv.ErpId
						}

						// 获取当前期数该产品已采数量
						if prd_item, ok := data.PurchaseOrderPeriod[fmt.Sprintf("%d%d", prdinfo.Id, pdtid)]; ok {
							purchased_nums = prd_item.Order
						}
						// 采购单期数产品数据
						p_item_key := fmt.Sprintf("%d%s", o.Period, vv.ShortCode)
						if item, ook := p_period_item[p_item_key]; ook {
							// 已售
							item.SaleNums += sale_nums
							// 未发货数量 = 已售数量
							number = sale_nums
							// 未发货数量
							if o.PushWmsStatus == 1 || o.PushWmsStatus == 3 || o.SubOrderStatus != 1 {
								number = 0
							}
							// 未发货数量
							item.Number += number
							p_period_item[p_item_key] = item

						} else {
							// 未发货数量 = 已售数量
							number = sale_nums
							// 萌芽推送状态：0-未推送 1-推送成功 2-推送失败 3-不推送
							if o.PushWmsStatus == 1 || o.PushWmsStatus == 3 || o.SubOrderStatus != 1 {
								number = 0
							}
							p_period_item[p_item_key] = PurchaseOrdernoPeriod{
								Period:             o.Period,
								PeriodsType:        prdinfo.PeriodsType,
								ShortCode:          vv.ShortCode,
								Number:             number,
								SaleNums:           sale_nums,
								PurchasedNums:      purchased_nums,
								IsGift:             pdt.IsGift,
								IsSupplierDelivery: prdinfo.IsSupplierDelivery,
							}
						}

						// 非代发，非烈酒，已采数量0为首次采购
						if prdinfo.IsSupplierDelivery == 0 && prdinfo.IsLiejue == 0 && purchased_nums == 0 {
							is_first = 1
						}

						// 采购单产品数据
						item_key := vv.ShortCode
						if item, ook := p_order_item[item_key]; ook {
							// 当前期数为最新期数使用，最新期数成本价
							if l.IsMaxPeriod(item.Period, period) {
								item.Price = vv.Costprice
							}

							// 期数
							item.Period = common.UniqueStrings(fmt.Sprintf("%s,%d", item.Period, period), ",")

							// 更新是否首次采购
							if item.IsFirst == 1 {
								item.IsFirst = is_first
							}
							// 期数信息
							if prdinfo.IsSupplierDelivery == 1 {
								// 是否代发
								item.IsSupplierDelivery = prdinfo.IsSupplierDelivery
							}

							p_order_item[item_key] = item

						} else {
							// 税率
							tax_rate := vv.TaxRate
							if supplier_info.SupplierTax != "" {
								// 字符串转浮点数
								tax_rate, _ = strconv.ParseFloat(supplier_info.SupplierTax, 64)
							}
							invoice_name := vv.InvoiceName
							if invoice_name == "" {
								invoice_name = vv.CnProductName
							}

							p_order_item[item_key] = PurchaseOrdernoItems{
								ShortCode:          vv.ShortCode,
								BillingName:        invoice_name,
								EnProductName:      vv.EnProductName,
								Unit:               vv.ProductUnitName,
								Capacity:           vv.Capacity,
								Period:             fmt.Sprintf("%d", period),
								Price:              vv.Costprice,
								TaxRate:            tax_rate,
								IsGift:             pdt.IsGift,
								IsFirst:            is_first,
								IsSupplierDelivery: prdinfo.IsSupplierDelivery,
							}
						}
					}
					// 采购单
					purchase_order[key] = p_order
					// 采购单产品
					purchase_order_item[key] = p_order_item
					// 采购单期数产品
					period_items[key] = p_period_item
				}
			}
		}
	}
	// 取出可下单的采购单
	purchase_orders := make([]PurchaseOrderno, 0)
	new_order_item := make(map[string]map[string]PurchaseOrdernoItems)
	new_period_items := make(map[string]map[string]PurchaseOrdernoPeriod)
	for key, v := range purchase_order {
		if len(purchase_order_item[key]) == 0 {
			continue
		}
		var (
			order_period_str string
			one_period       int64
		)
		// 虚拟仓ID
		fictitious_id, _ := strconv.ParseInt(v.WarehouseCode, 10, 64)

		// 采购单期数设置默认值
		for kk, vv := range period_items[key] {
			var (
				beihuo          PreparePurchaseList
				use_beihuo_nums int64
				order_num       int64
				wms_stock       int64
			)
			// 萌牙库存数据
			wms_key := fmt.Sprintf("%s_%d", vv.ShortCode, fictitious_id)
			if s, ok := data.WmsStockDatas[wms_key]; ok {
				wms_stock = s.GoodsCount
			}
			// 备货数据
			p_key := fmt.Sprintf("%s_%s", vv.ShortCode, v.Supplier)
			if val, ok := data.PreparePurchaseDatas[p_key]; ok {
				beihuo = val
			}

			// 备货数量大于0，已售数量大于已采，使用备货数量
			if beihuo.Number > 0 && vv.SaleNums > vv.PurchasedNums {
				// 使用备货数量
				use_beihuo_nums = vv.SaleNums - vv.PurchasedNums
				if use_beihuo_nums > beihuo.Number {
					use_beihuo_nums = beihuo.Number
				}
				// 使用备货数据
				use_beihuo_data = append(use_beihuo_data, UseBeiHuoData{
					Period:      vv.Period,
					PeriodsType: vv.PeriodsType,
					ShortCode:   vv.ShortCode,
					BeiHuoId:    beihuo.Id,
					UseNums:     use_beihuo_nums,
				})
				// 修改已采数量
				vv.PurchasedNums = vv.PurchasedNums + use_beihuo_nums
				// 更新备货数据
				beihuo.Number = beihuo.Number - use_beihuo_nums
				data.PreparePurchaseDatas[p_key] = beihuo
			}

			// 代发
			if vv.IsSupplierDelivery == 1 {
				// 下单数量 = 已售-已采
				order_num = vv.SaleNums - vv.PurchasedNums

			} else { // 非代发
				//已采（+备货）＞=已售，无未发，不下单
				if vv.PurchasedNums >= vv.SaleNums || vv.Number <= 0 {
					order_num = 0

					//已售＞已采（备货）
				} else {
					//已售<（未发+已采），下单=已售-已采
					if vv.SaleNums < (vv.Number + vv.PurchasedNums) {
						order_num = vv.SaleNums - vv.PurchasedNums
					} else {
						//已售>=（未发+已采），下单=未发-库存
						order_num = vv.Number - wms_stock
					}
				}
			}
			// 下单数量大于0
			if order_num > 0 {
				if new_period_items[key] == nil {
					new_period_items[key] = make(map[string]PurchaseOrdernoPeriod)
				}
				new_period_items[key][kk] = vv
			}
		}
		if len(new_period_items[key]) == 0 {
			continue
		}

		for kk, vv := range purchase_order_item[key] {
			var (
				unshipped_num  int64
				purchased_nums int64
				period_str     string
				sale_nums      int64
				wms_stock      int64
				item_number    int64
			)

			// 采购单期数处理
			periodSlice := strings.Split(vv.Period, ",")
			for _, pv := range new_period_items[key] {
				if common.InArrayStr(fmt.Sprintf("%d", pv.Period), periodSlice) && vv.ShortCode == pv.ShortCode {
					if period_str == "" {
						period_str = fmt.Sprintf("%d", pv.Period)
					} else {
						period_str = fmt.Sprintf("%s,%d", period_str, pv.Period)
					}
					unshipped_num += pv.Number
					purchased_nums += pv.PurchasedNums
					sale_nums += pv.SaleNums
				}
			}

			// 萌牙库存数据
			wms_key := fmt.Sprintf("%s_%d", vv.ShortCode, fictitious_id)
			if s, ok := data.WmsStockDatas[wms_key]; ok {
				wms_stock = s.GoodsCount
			}

			// 代发
			if vv.IsSupplierDelivery == 1 {
				// 下单数量 = 已售-已采
				item_number = sale_nums - purchased_nums

			} else { // 非代发
				//已采（+备货）＞=已售，无未发，不下单
				if purchased_nums >= sale_nums || unshipped_num <= 0 {
					item_number = 0

					//已售＞已采（备货）
				} else {
					//已售<（未发+已采），下单=已售-已采
					if sale_nums < (unshipped_num + purchased_nums) {
						item_number = sale_nums - purchased_nums
					} else {
						//已售>=（未发+已采），下单=未发-库存
						item_number = unshipped_num - wms_stock
					}
				}
			}

			if item_number > 0 {
				// 已售
				vv.SaleNums = sale_nums
				// 未发货数量
				vv.UnshippedNum = unshipped_num
				// 期数
				period_arr := strings.Split(period_str, ",")
				// 期数
				vv.Period = common.UniqueStrings(period_str, ",")
				// 已采数量
				vv.PurchasedNums = purchased_nums
				// 下单数量
				vv.Number = fmt.Sprintf("%d", item_number)
				// 总金额
				vv.Total = common.BcMul(vv.Price, float64(item_number), 2)
				if new_order_item[key] == nil {
					new_order_item[key] = make(map[string]PurchaseOrdernoItems)
				}
				new_order_item[key][kk] = vv

				// 第一个期数
				if one_period == 0 {
					one_period, _ = strconv.ParseInt(period_arr[0], 10, 64)
				}
				// 采购单期数
				if order_period_str == "" {
					order_period_str = period_str
				} else {
					order_period_str = order_period_str + "," + period_str
				}
			}
		}

		if len(new_order_item) == 0 {
			continue
		}

		data.Count++
		// 生成采购订单时采购员使用第一个期数的对应采购
		if p_val, ok := data.PeriodInfoMap[one_period]; ok {
			v.Operator = p_val.BuyerId
			v.OperatorName = p_val.BuyerName
			if staff, ok := data.StaffMap[p_val.BuyerName]; ok {
				v.OperatorCode = staff.StaffCode
			}
		}
		// 采购单期数
		v.Period = common.UniqueStrings(order_period_str, ",")
		// 生成采购单号
		v.Orderno = l.GetPoNo(data.Count)
		purchase_orders = append(purchase_orders, v)
	}

	// 开启事务
	err := l.Db.Transaction(func(tx *gorm.DB) error {
		if len(purchase_orders) > 0 {
			// 创建采购单
			err := tx.Create(purchase_orders).Error
			if err != nil {
				return err
			}
		}
		// 产品数据
		orders_items := make([]PurchaseOrdernoItems, 0)
		// 期数数据
		orders_periods := make([]PurchaseOrdernoPeriod, 0)
		for _, v := range purchase_orders {
			key := fmt.Sprintf("%s%d", v.Supplier, v.PayeeMerchantId)
			// 提取产品
			for _, vv := range new_order_item[key] {
				periodSlice := strings.Split(vv.Period, ",")
				// 最大期数
				maxPeriod := common.GetMaxValueString(periodSlice)
				item_number, _ := strconv.ParseInt(vv.Number, 10, 64)
				if vv.IsFirst == 1 {
					item_number = l.FirstQuantityCalculation(item_number)
					for pkey, pv := range new_period_items[key] {
						periodItem := new_period_items[key][pkey]
						if len(periodSlice) == 1 {
							if common.InArrayStr(fmt.Sprintf("%d", pv.Period), periodSlice) && vv.ShortCode == pv.ShortCode {
								periodItem.Number = item_number
							}

						} else { // 多期数剩余数量分配给最大期数
							if pv.Period == maxPeriod && vv.ShortCode == pv.ShortCode {
								max_number := item_number
								for _, pvv := range new_period_items[key] {
									if common.InArrayStr(fmt.Sprintf("%d", pvv.Period), periodSlice) && pvv.Period != maxPeriod && vv.ShortCode == pv.ShortCode {
										max_number = max_number - pvv.Number
									}
								}
								periodItem.Number = max_number
							}
						}
						new_period_items[key][pkey] = periodItem
					}
				}
				// 下单数量
				vv.Number = fmt.Sprintf("%d", item_number)
				// 采购单ID
				vv.PurchaseOrdernoId = v.Id
				vv.Remark = fmt.Sprintf("已售 %d", vv.SaleNums)
				orders_items = append(orders_items, vv)
			}

			// 提取期数
			for _, vv := range new_period_items[key] {
				vv.CreatedTime = v.CreatedTime
				// 采购ID
				vv.PurchaseOrdernoId = v.Id
				orders_periods = append(orders_periods, vv)
			}
		}

		if len(orders_items) > 0 {
			// 保存采购单产品
			err := tx.Create(orders_items).Error
			if err != nil {
				return err
			}
		}
		if len(orders_periods) > 0 {
			// 保存采购单期数
			err := tx.Create(orders_periods).Error
			if err != nil {
				return err
			}
		}

		// 返回 nil 提交事务
		return nil
	})

	if err != nil {
		logger.E("采购单插入失败", err)
		return
	}

	// 更新备货数据
	go l.UpdateBeihuoData(&use_beihuo_data)

	// 修改下次采购时间
	l.UpdatePurchaseTime(periods, data.PeriodInfoMap)
}
