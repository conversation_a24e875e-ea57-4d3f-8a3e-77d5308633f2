package second

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetFiltersListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetFiltersListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetFiltersListLogic {
	return &GetFiltersListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

type GetLabelsResponse struct {
	Data GetLabelsResponseData `json:"data"`
}
type GetLabelsResponseData struct {
	ThreeHoursTag bool `json:"three_hours_tag"`
	TomorrowTag   bool `json:"tomorrow_tag"`
}

func (l *GetFiltersListLogic) GetFiltersList(req *types.GetFiltersListReq) (resp *types.GetFiltersListResp, err error) {
	var (
		data        []PeriodsSecondFilters
		result      types.GetFiltersListResp
		labels_resp GetLabelsResponse
		wg          sync.WaitGroup
	)
	wg.Add(2)
	//根据用户位置查询标签显示
	go func() {
		defer wg.Done()
		if req.Longitude != "" && req.Latitude != "" {
			url := l.svcCtx.Config.ITEM.VMALL_URL + "/vmall/v3/goods/getLabels"
			body := map[string]string{
				"lng": req.Longitude,
				"lat": req.Latitude,
			}
			header := map[string]string{}
			res, _ := l.svcCtx.HttpClient.Get(url, body, header)
			json.Unmarshal(res.Body(), &labels_resp)
		}
	}()
	//查询筛选标签
	go func() {
		defer wg.Done()
		l.svcCtx.DbCommodities.Model(&PeriodsSecondFilters{}).
			Select("id", "title", "identifier", "filters_type").
			Where("status = 1").
			Order("weight_value desc").
			Scan(&data)
	}()
	wg.Wait()

	list := make([]types.GetFiltersListData, 0)
	if len(data) > 0 {
		for _, v := range data {
			//三小时达
			if !labels_resp.Data.ThreeHoursTag && v.Identifier == "sxsd" {
				continue
			}
			//次日达/本地仓
			if !labels_resp.Data.TomorrowTag && v.Identifier == "bdc" {
				continue
			}
			//运营标签
			if v.FiltersType == 1 {
				v.Identifier = fmt.Sprintf("%s_%d", v.Identifier, v.Id)
			}

			list = append(list, types.GetFiltersListData{
				Title:      v.Title,
				Identifier: v.Identifier,
			})
		}
	}
	result = types.GetFiltersListResp{
		List: list,
	}

	return &result, nil
}
