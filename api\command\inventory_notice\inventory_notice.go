package inventory_notice

import (
	"encoding/json"
	"engine/api/internal/svc"
	"engine/api/service/work_weixin"
	"engine/common/config"
	"engine/common/logger"
	"engine/common/xtime"
	"fmt"
	"strings"
	"sync"
	"time"
)

type InventoryNoticeLogic struct {
	SvcCtx *svc.ServiceContext
}

var (
	// userid = []string{"GanGaoHan", "XuanYiChang"}
	userid = []string{"BaiRiMengXiangJia", "LongFei", "ChenHongZhou", "YuMengYang", "ZhuChengHao", "LiXiangDian"}

	// 闪购仓、秒发仓、食品仓、微醺酒业南通仓
	fictitious_ids = []int64{4, 13, 314, 338}
)

func NewInventoryNoticeLogic(svcCtx *svc.ServiceContext) *InventoryNoticeLogic {
	// 修改测试环境通知人
	if !strings.Contains(svcCtx.Config.ITEM.ALIURL, "vinehoo") {
		userid = []string{"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}
	}

	return &InventoryNoticeLogic{
		SvcCtx: svcCtx,
	}
}

// 导出【闪购、秒发、食品】仓有库存简码，判断酒云网是否在售，以表格形式每天早上7点推送到企微中台【杨文科、龙飞、陈泓州】：https://devops.aliyun.com/projex/project/8ab3146db7bf4a79876e3a5333/task#viewIdentifier=1945bcda00c0cf935ebdd8a5&openWorkitemIdentifier=26f7eadb7f3930c517c30e400e
func (l *InventoryNoticeLogic) Execute() {
	var (
		hour   int
		minute int
	)
	// 测试环境不通知
	if !strings.Contains(l.SvcCtx.Config.ITEM.ALIURL, "vinehoo") {
		return
	}

	for {
		hour = time.Now().Hour()
		minute = time.Now().Minute()

		// 以表格形式每天早上7点推送到企微中台【杨文科、龙飞、陈泓州】
		if hour == 7 && minute == 0 {
			fmt.Println("导出【闪购、秒发、食品】仓有库存简码--start")
			l.Handle()
		}
		fmt.Println("导出【闪购、秒发、食品】仓有库存简码--end")
		// 延迟
		time.Sleep(1 * time.Minute)
	}
}

func (l *InventoryNoticeLogic) Handle() {
	var (
		fictitious_goods          []FictitiousGoodsData
		periods_product_inventory []PeriodsProductInventory
		short_codes               []string
		wg                        sync.WaitGroup
		all_periods_data          []PeriodsData
	)

	// excel数据
	excel_data := [][]interface{}{
		{"简码", "中文名", "英文名", "库存数量", "在售期数", "昨日动销", "7日动销", "30日动销", "采购", "入库时间"},
	}
	// 设置昨天0点时间戳
	yesterday := xtime.GetDayStartTimestamp(1)
	// 设置7天前0点时间戳
	sevenDaysAgo := xtime.GetDayStartTimestamp(7)
	// 设置30天前0点时间戳
	thirtyDaysAgo := xtime.GetDayStartTimestamp(30)
	// 转换今天为时间戳
	todayUnix := xtime.GetDayStartTimestamp(0)

	// 查询库存
	l.SvcCtx.DbWms.Table("wms_fictitious_goods fg").
		Joins("left join wms_goods g on fg.bar_code = g.bar_code").
		Where("fg.fictitious_id IN ? and fg.goods_count > 0", fictitious_ids).
		Select("g.short_code,fg.bar_code,g.goods_name,g.en_goods_name,sum(fg.goods_count) as goods_count").
		Group("fg.bar_code").
		Scan(&fictitious_goods)

	if len(fictitious_goods) > 0 {
		// 获取所有简码
		for _, v := range fictitious_goods {
			short_codes = append(short_codes, v.ShortCode)
		}
		storage_date_map := make(map[string]string)

		wg.Add(2)
		go func() {
			defer wg.Done()
			var storage_goods []StorageGoods
			// 查询最近一次采购上架日期
			l.SvcCtx.DbWms.Table("wms_storage_up_task_goods ug").
				Joins("left join wms_storage_goods sg on ug.storage_goods_id = sg.storage_goods_id").
				Joins("left join wms_storage_up_task ut on ut.task_id = ug.task_id").
				Where("sg.short_code IN ? and ut.task_finish_time > 0", short_codes).
				Select("sg.short_code,max(ut.task_finish_time) as task_finish_time").
				Group("sg.short_code").
				Scan(&storage_goods)
			for _, v := range storage_goods {
				storage_date_map[v.ShortCode] = xtime.DateFormat("2006-01-02", v.TaskFinishTime)
			}
		}()
		go func() {
			defer wg.Done()
			// 查询期数产品
			l.SvcCtx.DbCommodities.Model(PeriodsProductInventory{}).
				Where("short_code IN ? and periods_type IN(0,1,3) and is_use_comment = 0 and warehouse_id IN ?", short_codes, fictitious_ids).
				Order("period desc").
				Scan(&periods_product_inventory)
		}()
		wg.Wait()

		if len(periods_product_inventory) > 0 {
			// 期数ID分类
			period_class := make(map[int64][]int64)
			// 产品数据
			periods_product := make(map[int64]PeriodsProductInventory)
			for _, v := range periods_product_inventory {
				if _, ok := period_class[v.PeriodsType]; !ok {
					period_class[v.PeriodsType] = make([]int64, 0)
				}
				period_class[v.PeriodsType] = append(period_class[v.PeriodsType], v.Period)
				periods_product[v.ProductId] = v
			}

			// 查询在售期数
			var order_data_arr []OrderData
			for k, v := range period_class {
				wg.Add(1)
				go func(k int64, v []int64) {
					var (
						periods_data []PeriodsData
						order_data   []OrderData
					)
					defer wg.Done()
					// 期数表
					period_table := config.PeriodTable[k]
					// 订单表
					order_table := config.OrderTable[k]
					// 套餐表
					package_table := config.PackageTable[k]

					// 查询在售期数
					l.SvcCtx.DbCommodities.Table(period_table).
						Where("id IN ?", v).
						Order("id desc").
						Scan(&periods_data)
					all_periods_data = append(all_periods_data, periods_data...)

					// 查询销量
					l.SvcCtx.DbOrders.Table(order_table+" o").
						Joins("left join vh_order_main om on om.id = o.main_order_id").
						Joins("left join vh_order_mystery_box_log box on box.main_order_no = om.main_order_no").
						Joins(fmt.Sprintf("left join vh_commodities.%s pkg on pkg.id = o.package_id", package_table)).
						Where("o.period IN ? and o.payment_time >= ? and o.payment_time < ? and o.sub_order_status IN(1,2,3)", v, thirtyDaysAgo, todayUnix).
						Select("o.id,o.order_qty,o.period,o.package_id,o.payment_time,box.product_info,pkg.associated_products").
						Scan(&order_data)
					order_data_arr = append(order_data_arr, order_data...)
				}(k, v)
			}
			wg.Wait()

			// 销量处理
			product_sales := make(map[string][]ProductSales)
			for _, o := range order_data_arr {
				var product_info []PeriodsProductInfo
				if o.ProductInfo != "" {
					json.Unmarshal([]byte(o.ProductInfo), &product_info)
				} else {
					json.Unmarshal([]byte(o.AssociatedProducts), &product_info)
				}
				for _, p := range product_info {
					// 销售瓶数
					number := p.Nums * o.OrderQty
					// 产品信息
					if product, ok := periods_product[p.ProductId]; ok {
						if _, ok := product_sales[product.ShortCode]; !ok {
							product_sales[product.ShortCode] = make([]ProductSales, 0)
						}
						product_sales[product.ShortCode] = append(product_sales[product.ShortCode], ProductSales{
							PaymentTime: o.PaymentTime,
							Nums:        number,
						})
					}
				}
			}

			// 在售期数集合
			period_map := make(map[int64]PeriodsData)
			channel_period_map := make(map[int64]PeriodsData)
			all_period_map := make(map[int64]PeriodsData)
			for _, v := range all_periods_data {
				all_period_map[v.Id] = v

				if v.OnsaleStatus != 2 {
					continue
				}

				if v.IsChannel == 1 {
					channel_period_map[v.Id] = v
				} else {
					period_map[v.Id] = v
				}
			}

			// 简码对应在售期数
			short_code_period := make(map[string][]int64)
			for _, v := range periods_product_inventory {
				if _, ok := short_code_period[v.ShortCode]; !ok {
					short_code_period[v.ShortCode] = make([]int64, 0)
				}
				short_code_period[v.ShortCode] = append(short_code_period[v.ShortCode], v.Period)
			}

			// 创建今日数据库记录并计算动销
			var inventoryRecords []WmsInventoryRecords
			//组装数据
			for _, v := range fictitious_goods {
				var (
					period_id         []string
					channel_period_id string
					period_id_str     string
					yesterdaySales    int64 = 0 // 昨日动销
					sevenDaysSales    int64 = 0 // 7日动销
					thirtyDaysSales   int64 = 0 // 30日动销
					buyer_name        string
					storage_date      string
				)
				if val, ok := storage_date_map[v.ShortCode]; ok {
					storage_date = val
				}

				if sales, ok := product_sales[v.ShortCode]; ok {
					for _, s := range sales {
						if s.PaymentTime >= yesterday { // 昨日动销
							yesterdaySales = s.Nums + yesterdaySales
						}
						if s.PaymentTime >= sevenDaysAgo { // 7日动销
							sevenDaysSales = s.Nums + sevenDaysSales
						}
						if s.PaymentTime >= thirtyDaysAgo { // 30日动销
							thirtyDaysSales = s.Nums + thirtyDaysSales
						}
					}
				}

				if period, ok := short_code_period[v.ShortCode]; ok {
					for _, p := range period {
						// 非渠道在售期数
						if _, ok := period_map[p]; ok {
							period_id = append(period_id, fmt.Sprintf("%d", p))
						}
						// 渠道在售期数
						if _, ok := channel_period_map[p]; ok && channel_period_id == "" {
							channel_period_id = fmt.Sprintf("%d（渠道）", p)
						}

						if val, ok := all_period_map[p]; ok {
							if buyer_name == "" {
								buyer_name = val.BuyerName
							}
						}
					}
				}

				// 如果该简码只有渠道期数在售卖就只能显示渠道期数，如果有非渠道在售就只显示非渠道销售
				if len(period_id) > 0 {
					period_id_str = strings.Join(period_id, ",")
				} else if channel_period_id != "" {
					period_id_str = channel_period_id
				}

				// 添加到Excel数据
				excel_data = append(excel_data, []interface{}{
					v.ShortCode,
					v.GoodsName,
					v.EnGoodsName,
					v.GoodsCount,
					period_id_str,
					yesterdaySales,
					sevenDaysSales,
					thirtyDaysSales,
					buyer_name,
					storage_date,
				})

				// 创建数据库记录
				inventoryRecords = append(inventoryRecords, WmsInventoryRecords{
					ShortCode:      v.ShortCode,
					BarCode:        v.BarCode,
					GoodsName:      v.GoodsName,
					EnGoodsName:    v.EnGoodsName,
					GoodsCount:     v.GoodsCount,
					OnSalePeriods:  period_id_str,
					YesterdaySales: yesterdaySales,
					SevenDaySales:  sevenDaysSales,
					ThirtyDaySales: thirtyDaysSales,
					Date:           time.Now(),
					CreatedAt:      todayUnix,
				})
			}

			// 批量保存数据到数据库
			if len(inventoryRecords) > 0 {
				var record_exist int64
				l.SvcCtx.DbCommodities.Model(&WmsInventoryRecords{}).
					Where("created_at >= ?", todayUnix).
					Count(&record_exist)
				if record_exist == 0 {
					if err := l.SvcCtx.DbCommodities.CreateInBatches(inventoryRecords, 100).Error; err != nil {
						logger.E("保存萌牙库存记录失败: ", err)
					}
				}
			}
		}
	}

	weixin := work_weixin.NewWorkWeixinLogic(l.SvcCtx)
	weixin.SendFileNotice(excel_data, "【闪购、秒发、食品】仓有库存简码.xlsx", userid)
}
