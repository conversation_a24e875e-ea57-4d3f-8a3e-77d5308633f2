package svc

import (
	"engine/api/internal/middleware"
	"engine/common/config"
	"engine/common/esClient"
	"engine/common/httpClient"
	"engine/common/mysql"
	"engine/common/redigo"
	"engine/common/validation"

	"github.com/zeromicro/go-zero/rest"
	"gorm.io/gorm"
)

type ServiceContext struct {
	Config              config.ApiConfig
	DbCommodities       *gorm.DB
	DbMarketing         *gorm.DB
	DbNews              *gorm.DB
	DbCommunity         *gorm.DB
	DbAuction           *gorm.DB
	DbUser              *gorm.DB
	DbOrders            *gorm.DB
	DbWms               *gorm.DB
	DbWiki              *gorm.DB
	DbFollowWiki        *gorm.DB
	DbFollowCommodities *gorm.DB
	Es                  *esClient.Esconfig
	RDbSix              *redigo.ClientConfig
	HttpClient          *httpClient.HttpConfig
	Verify              *validation.Verify
	Global              rest.Middleware
	User                rest.Middleware
}

func NewServiceContext(c config.ApiConfig) *ServiceContext {
	//商品数据库
	db_commodities := mysql.Connect(c.DATABASE_COMMODITIES, 500)
	//营销数据库
	db_marketing := mysql.Connect(c.DATABASE_MARKETING, 200)
	//资讯数据库
	db_news := mysql.Connect(c.DATABASE_NEWS, 200)
	//社区数据库
	db_community := mysql.Connect(c.DATABASE_COMMUNITY, 200)
	//拍卖数据库
	db_auction := mysql.Connect(c.DATABASE_AUCTION, 10)
	//用户数据库
	db_user := mysql.Connect(c.DATABASE_USER, 100)
	//订单数据库
	db_orders := mysql.Connect(c.DATABASE_ORDERS, 200)
	//萌牙数据库
	db_wms := mysql.Connect(c.DATABASE_WMS, 100)
	//磐石数据库
	db_wiki := mysql.Connect(c.DATABASE_WIKI, 100)
	//磐石从数据库
	c.FOLLOW_DATABASE.Database = "vh_wiki"
	db_follow_wiki := mysql.Connect(c.FOLLOW_DATABASE, 100)
	//商品从数据库
	c.FOLLOW_DATABASE.Database = "vh_commodities"
	db_follow_commodities := mysql.Connect(c.FOLLOW_DATABASE, 100)

	//redis
	rdb_six := redigo.Handle(c.Redis, 6, 100)
	//es
	es := esClient.Handle(c.Es.Host, c.Es.Username, c.Es.Password, c.Es.Prefix)
	//redis db11
	// rdb_eleven := redigo.Handle(c.Redis, 11, 100)

	return &ServiceContext{
		Config:              c,
		DbCommodities:       db_commodities,
		DbMarketing:         db_marketing,
		RDbSix:              rdb_six,
		DbNews:              db_news,
		DbCommunity:         db_community,
		DbAuction:           db_auction,
		DbUser:              db_user,
		DbOrders:            db_orders,
		DbWms:               db_wms,
		DbWiki:              db_wiki,
		DbFollowWiki:        db_follow_wiki,
		DbFollowCommodities: db_follow_commodities,
		Es:                  es,
		HttpClient:          httpClient.Handle(c),
		Verify:              validation.NewVerify(),
		Global:              middleware.NewGlobalMiddleware().Handle,
		User:                middleware.NewUserMiddleware().Handle,
	}
}
