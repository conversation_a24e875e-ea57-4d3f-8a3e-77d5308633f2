package data_analysis

import (
	"context"
	"encoding/json"
	"fmt"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/esClient"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
)

type ProductSaleFollowDatasLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewProductSaleFollowDatasLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ProductSaleFollowDatasLogic {
	return &ProductSaleFollowDatasLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ProductSaleFollowDatasLogic) ProductSaleFollowDatas(req *types.ProductSaleFollowDatasReq) (resp *types.ProductSaleFollowDatasResp, err error) {
	var result types.ProductSaleFollowDatasResp

	resp = &result

	products := make(map[string]types.ProductSaleFollowProducts)
	periods := make(map[int64]types.ProductSaleFollowPeriodsPeriods)
	packages := make(map[int64][]types.ProductSaleFollowPackages)
	sale_num := make(map[string]interface{})
	threeday_sale_num := make(map[string]interface{})
	my_inventory_data := make(map[string]int64)
	periods_product := make(map[string]types.ProductSaleFollowPeriodsProduct)
	storage_goods := make(map[string]types.ProductSaleFollowStorageGoods)

	mr.Finish(func() (err error) {
		var info []types.ProductSaleFollowProducts
		// 查询产品信息
		l.svcCtx.DbFollowWiki.Table("vh_products as p").
			Joins("left join vh_country_base c on p.country_id = c.id").
			Joins("left join vh_winery_base w on w.id=p.chateau_id").
			Joins("left join vh_product_type t on t.id=p.product_type").
			Where("p.short_code IN ?", req.ShortCode).
			Select("p.id,p.short_code,p.bar_code,p.cn_product_name,p.en_product_name,COALESCE(c.country_name_cn,'') as 'country_name_cn',COALESCE(w.winery_name_cn,'') as 'winery_name_cn',t.name as product_type_name").
			Scan(&info)
		for _, v := range info {
			products[v.ShortCode] = v
		}
		return
	}, func() (err error) {
		if req.Type == 1 {
			return
		}

		var (
			info        []types.ProductSaleFollowPeriodsPeriods
			query_param []map[string]interface{}
		)
		// 查询期数信息
		Es := esClient.New(l.svcCtx.Config)
		where := [][]interface{}{
			{"_id", "in", req.PeriodId},
		}
		Es.Name("periods").Where(where).Field([]string{
			"id",
			"onsale_status",
			"periods_type",
			"import_type",
			"supplier",
			"supplier_id",
			"buyer_name",
			"buyer_id",
			"operation_name",
			"operation_id",
			"is_channel",
		}).Select(&info)
		// 查询简码真实已售
		for _, v := range info {
			periods[v.Id] = v
			query_param = append(query_param, map[string]interface{}{
				"period":      v.Id,
				"period_type": v.PeriodsType,
			})
		}

		mr.Finish(func() (err error) { // 查询销售瓶数
			var res_body GetSaleBottleNumsResp
			url_str := l.svcCtx.Config.ITEM.USER_CACHE_URL + "/commodities/GetSaleBottleNums"
			res, _ := l.svcCtx.HttpClient.PostJson(url_str, query_param, map[string]string{})
			json.Unmarshal(res.Body(), &res_body)
			sale_num = res_body.Data
			return

		}, func() (err error) { //查询近3天销售瓶数
			var res_body GetSaleBottleNumsResp
			param := map[string]interface{}{
				"query_type":  1,
				"period_data": query_param,
			}
			url_str := l.svcCtx.Config.ITEM.USER_CACHE_URL + "/commodities/GetCustomSaleBottleNums"
			res, _ := l.svcCtx.HttpClient.PostJson(url_str, param, map[string]string{})
			json.Unmarshal(res.Body(), &res_body)
			threeday_sale_num = res_body.Data
			return
		})

		return
	}, func() (err error) {
		if req.Type == 1 {
			return
		}
		var info []types.ProductSaleFollowPackages
		// 查询套餐信息
		Es := esClient.New(l.svcCtx.Config)
		where := [][]interface{}{
			{"period_id", "in", req.PeriodId},
			{"is_hidden", "=", 0},
		}
		Es.Name("periods_set").Where(where).Field([]string{
			"id",
			"period_id",
			"package_name",
			"price",
			"associated_products",
		}).Select(&info)
		for _, v := range info {
			packages[v.PeriodId] = append(packages[v.PeriodId], v)
		}
		return
	}, func() (err error) {
		if req.Type == 1 {
			return
		}
		// 查询萌牙库存
		var res_body GoodsGetFictitiousCountResp
		query_param := map[string]interface{}{
			"short_code": req.ShortCode,
			"store_code": "xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa",
		}
		url_str := l.svcCtx.Config.ITEM.WMS_DISTRIBUTE_URL + "/query/goodsGetFictitiousCount"
		res, _ := l.svcCtx.HttpClient.PostJson(url_str, query_param, map[string]string{})
		json.Unmarshal(res.Body(), &res_body)
		for k, v := range res_body.Data {
			for _, vv := range v {
				if _, ok := my_inventory_data[k]; !ok {
					my_inventory_data[k] = 0
				}
				my_inventory_data[k] += vv.GoodsCount
			}
		}
		return
	}, func() (err error) {
		if req.Type == 1 {
			return
		}
		// 查询期数产品信息
		var info []types.ProductSaleFollowPeriodsProduct
		l.svcCtx.DbFollowCommodities.Table("vh_periods_product_inventory").
			Where("short_code IN ? and period IN ?", req.ShortCode, req.PeriodId).
			Select("id,period,product_id,costprice,bar_code,short_code").
			Scan(&info)
		for _, v := range info {
			periods_product[fmt.Sprintf("%d_%s", v.Period, v.ShortCode)] = v
		}
		return
	}, func() (err error) {
		if req.Type == 1 {
			return
		}
		// 查询萌牙最近上架时间
		var info []types.ProductSaleFollowStorageGoods
		l.svcCtx.DbWms.Table("wms_storage_goods").
			Where("short_code IN ? and up_status in(2,3)", req.ShortCode).
			Select("bar_code,short_code,max(update_time) as update_time").
			Group("short_code").
			Scan(&info)
		for _, v := range info {
			storage_goods[v.ShortCode] = v
		}
		return
	})
	result.Products = products
	result.Periods = periods
	result.Packages = packages
	result.SaleNum = sale_num
	result.MyInventoryData = my_inventory_data
	result.PeriodsProduct = periods_product
	result.StorageGoods = storage_goods
	result.ThreeDaySaleNum = threeday_sale_num

	return
}
