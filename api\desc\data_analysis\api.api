syntax = "v1"

info(
    title: "数据分析"
    author: "gangh"
    email: "<EMAIL>"
    version: "v3"
)

type (
    OnlineGoodsRealtimeStatisticsReq {
        CountKey string `form:"count_key" validate:"required" v:"统计类型"`
        Month string `form:"month" validate:"required" v:"月份"`
    }

    OnlineGoodsRealtimeStatisticsResp {
        Date string `json:"date"`
        Week string `json:"week"`
        FlashNews int64 `json:"flash_news"`
        FlashSale int64 `json:"flash_sale"`
        FlashSales float64 `json:"flash_sales"`
        FlashOrders int64 `json:"flash_orders"`
        SecondNews int64 `json:"second_news"`
        SecondSale int64 `json:"second_sale"`
        SecondSales float64 `json:"second_sales"`
        SecondOrders int64 `json:"second_orders"`
        CrossNews int64 `json:"cross_news"`
        CrossSale int64 `json:"cross_sale"`
        CrossSales float64 `json:"cross_sales"`
        CrossOrders int64 `json:"cross_orders"`
        TailNews int64 `json:"tail_news"`
        TailSale int64 `json:"tail_sale"`
        TailSales float64 `json:"tail_sales"`
        TailOrders int64 `json:"tail_orders"`
        ChannelNews int64 `json:"channel_news"`
        ChannelSale float64 `json:"channel_sale"`
        ChannelSales float64 `json:"channel_sales"`
        ChannelOrders int64 `json:"channel_orders"`
        AuctionNews int64 `json:"auction_news"`
        AuctionSale float64 `json:"auction_sale"`
        AuctionSales float64 `json:"auction_sales"`
        AuctionOrders int64 `json:"auction_orders"`
        PersonalAuctionNews int64 `json:"personal_auction_news"`
        PersonalAuctionSale float64 `json:"personal_auction_sale"`
        PersonalAuctionSales float64 `json:"personal_auction_sales"`
        PersonalAuctionOrders int64 `json:"personal_auction_orders"`
    }

    ProductSaleFollowDatasReq {
        MainPeriod []int64 `json:"main_period" validate:"required" v:"最后一期期数"`
        ShortCode []string `json:"short_code" validate:"required" v:"简码"`
        PeriodId []int64 `json:"period_id" validate:"required" v:"所有期数"`
        Type int64 `json:"type,optional" validate:"omitempty" v:"查询类型：0-所有，1-产品信息"`
    }
    ProductSaleFollowDatasResp {
        Products map[string]ProductSaleFollowProducts `json:"products"`
        Packages map[int64][]ProductSaleFollowPackages `json:"packages"`
        Periods map[int64]ProductSaleFollowPeriodsPeriods `json:"periods"`
        SaleNum map[string]interface{} `json:"sale_num"`
        MyInventoryData map[string]int64 `json:"my_inventory_data"`
        PeriodsProduct map[string]ProductSaleFollowPeriodsProduct `json:"periods_product"`
        StorageGoods map[string]ProductSaleFollowStorageGoods `json:"storage_goods"`
        ThreeDaySaleNum map[string]interface{} `json:"threeday_sale_num"`
    }

    ProductSaleFollowStorageGoods{
        BarCode string `json:"bar_code"`
        ShortCode string `json:"short_code"`
        UpdateTime int64 `json:"update_time"`
    }

    ProductSaleFollowPeriodsPeriods {
        Id int64 `json:"id"`
        OnsaleStatus int64 `json:"onsale_status"`
        PeriodsType int64 `json:"periods_type"`
        ImportType int64 `json:"import_type"`
        Supplier string `json:"supplier"`
        SupplierId int64 `json:"supplier_id"`
        BuyerName string `json:"buyer_name"`
        BuyerId int64 `json:"buyer_id"`
        OperationName string `json:"operation_name"`
        OperationId int64 `json:"operation_id"`
        IsChannel int64 `json:"is_channel"`
    }
    ProductSaleFollowPeriodsProduct {
        Id int64 `json:"id"`
        Period    int64 `json:"period"`
	    ProductId int64 `json:"product_id"`
	    BarCode   string `json:"bar_code"`
        ShortCode string `json:"short_code"`
        Costprice float64 `json:"costprice"`
    }
    ProductSaleFollowProducts {
        Id int64 `json:"id"`
        ShortCode string `json:"short_code"`
        BarCode string `json:"bar_code"`
        CnProductName string `json:"cn_product_name"`
        EnProductName string `json:"en_product_name"`
        CountryNameCn string `json:"country_name_cn"`
        WineryNameCn string `json:"winery_name_cn"`
        ProductTypeName string `json:"product_type_name"`
    }
    ProductSaleFollowPackages{
        Id int64 `json:"id"`
        PeriodId int64 `json:"period_id"`
        PackageName string `json:"package_name"`
        Price float64 `json:"price"`
        AssociatedProducts string `json:"associated_products"`
    }

    GetPurchaseOrdersRealTimeDataReq {
        PurchaseOrderNo []string `json:"purchase_order_no,optional" validate:"omitempty" v:"采购单号"`
        PeriodId []int64 `json:"period_id" validate:"required" v:"所有期数"`
        ShortCode []string `json:"short_code" validate:"required" v:"简码"`
        SupplierName []string `json:"supplier_name" validate:"required" v:"供应商"`
        QueryType int64 `json:"query_type,optional" validate:"omitempty" v:"查询类型"`
    }

    GetPurchaseOrdersRealTimeDataResp{
        Periods map[int64]map[string]interface{} `json:"periods"`
        Products map[string]map[string]interface{} `json:"products"`
        Supplier map[string]map[string]interface{} `json:"supplier"`
        PeriodProduct map[string]map[string]interface{} `json:"period_product"`
        GrossMargin map[string]interface{} `json:"gross_margin"`
        SaleNum map[string]interface{} `json:"sale_num"`
        UnshippedNum map[string]interface{} `json:"unshipped_num"`
        MyInventory map[string]map[string]interface{} `json:"my_inventory"`
        Mystorage map[string]map[string]interface{} `json:"my_storage"`
    }

    GetTailInventoryReq{
        Page int64 `form:"page" validate:"required" v:"当前页"`
        Limit int64 `form:"limit" validate:"required" v:"返回条数"`
        ShortCode string `form:"short_code,optional" validate:"omitempty" v:"简码"`
        NotSale int64 `form:"not_sale,optional" validate:"omitempty" v:"不在售"`
    }
    GetTailInventoryResp{
        List []GetTailInventoryRespList `json:"list"`
        Total int64 `json:"total"`
    }
    GetTailInventoryRespList{
        Id                     int64 `json:"id"`
        CnGoodsName            string   `json:"cn_goods_name"`
        EnGoodsName            string   `json:"en_goods_name"`
        ShortCode              string   `json:"short_code"`
        BarCode                string   `json:"bar_code"`
        Inventory              []map[string]interface{}   `json:"inventory"`
        IsSale                 int64    `json:"is_sale"`
        Period                 string   `json:"period"`
        PeriodInfo             []map[string]interface{}   `json:"period_info"`
        RemainingSaleInventory []map[string]interface{}   `json:"remaining_sale_inventory"`
        CreatedTime            string   `json:"created_time"`
    }

    NewInventoryNoticeReq {
        Type int64 `form:"type,optional" validate:"omitempty" v:"类型"`
    }

    OneFlowerOneWorldKanbanReq{
        Page int64 `form:"page,optional" validate:"omitempty" v:"当前页"`
        Limit int64 `form:"limit,optional" validate:"omitempty" v:"返回条数"`
        ShortCode string `form:"short_code,optional" validate:"omitempty" v:"简码"`
        WarehouseCode string `form:"warehouse_code,optional" validate:"omitempty" v:"仓库编码"`
        Type int64 `form:"type,optional,default=1" validate:"omitempty" v:"类型:1=汇总,2=销售明细,3=采购明细"`
    }
    OneFlowerOneWorldKanbanResp{
        List []OneFlowerOneWorldKanbanRespList `json:"list"`
        Total int64 `json:"total"`
    }
    OneFlowerOneWorldKanbanRespList{
        Warehouse string `json:"warehouse"`
        WarehouseCode string `json:"warehouse_code"`
        ShortCode string `json:"short_code"`
        CnGoodsName            string   `json:"cn_goods_name"`
        Capacity string `json:"capacity"`
        Unit string `json:"unit"`
        PurchaseNum int64 `json:"purchase_num"`
        SaleNum int64 `json:"sale_num"`
        AllocationOutNum int64 `json:"allocation_out_num"`
        AllocationInNum int64 `json:"allocation_in_num"`
        InventoryNum int64 `json:"inventory_num"`
    }
)


@server(
    middleware: Global
    group : data_analysis
    prefix :/commodities_server/v3/data_analysis
)

service main {
    @handler OnlineGoodsRealtimeStatistics //线上商品实时统计
    get /OnlineGoodsRealtimeStatistics (OnlineGoodsRealtimeStatisticsReq) returns ([]OnlineGoodsRealtimeStatisticsResp)

    @handler ProductSaleFollowDatas // 查询运营产品销售跟进关注看板列表数据
    post /ProductSaleFollowDatas (ProductSaleFollowDatasReq) returns (ProductSaleFollowDatasResp)

    @handler GetPurchaseOrdersRealTimeData // 查询采购单列表查询实时数据
    post /GetPurchaseOrdersRealTimeData (GetPurchaseOrdersRealTimeDataReq) returns (GetPurchaseOrdersRealTimeDataResp)

    @handler GetTailInventory // 查询尾货库存页面
    get /GetTailInventory (GetTailInventoryReq) returns (GetTailInventoryResp)

    @handler GenerateTailInventory // 重新生成尾货库存页面缓存
    get /GenerateTailInventory

    @handler NewGeneratePurchaseOrder // 重新生成采购下单
    get /NewGeneratePurchaseOrder

    @handler NewInventoryNotice // 导出通知
    get /NewInventoryNotice (NewInventoryNoticeReq)

    @handler OneFlowerOneWorldKanban // 一花一世界看板
    get /OneFlowerOneWorldKanban (OneFlowerOneWorldKanbanReq) returns (OneFlowerOneWorldKanbanResp)

    @handler ExportOneFlowerOneWorldKanban // 导出一花一世界看板
    get /ExportOneFlowerOneWorldKanban (OneFlowerOneWorldKanbanReq)

}