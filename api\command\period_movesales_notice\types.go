package period_movesales_notice

type PeriodsData struct {
	Id          int64
	Title       string
	IsChannel   int64
	OnsaleTime  int64
	BuyerName   string
	Pageviews   int64
	PeriodsType string `gorm:"-"`
}

type PeriodsProductInventory struct {
	Period      int64
	PeriodsType int64
	ProductId   int64
	ShortCode   string
	Inventory   int64
	WarehouseId string
}

type FictitiousGoodsData struct {
	FictitiousId int64
	ShortCode    string
	BarCode      string
	GoodsName    string
	EnGoodsName  string
	GoodsCount   int64
}

type OrderData struct {
	Id                 int64 `gorm:"primaryKey"`
	OrderQty           int64
	Period             int64
	PackageId          int64
	PaymentTime        int64
	ProductInfo        string
	AssociatedProducts string
}

type PeriodsProductInfo struct {
	ProductId int64 `json:"product_id"`
	Nums      int64 `json:"nums"`
}

type ProductSales struct {
	PaymentTime int64
	Nums        int64
}

type EsPeriodsInfo struct {
	Id              int64 `json:"id"`
	Purchased<PERSON><PERSON> int64 `json:"purchased_person"`
}
