package httpClient

import (
	"encoding/json"
	"engine/common/config"
	"engine/common/logger"
	"fmt"
	"time"

	"github.com/go-resty/resty/v2"
)

type HttpConfig struct {
	Client *resty.Client
	Config config.ApiConfig
}

func Handle(c config.ApiConfig) *HttpConfig {
	return &HttpConfig{
		Client: resty.New(),
		Config: c,
	}
}

func (h *HttpConfig) Get(Url string, Param, Header map[string]string) (*resty.Response, error) {
	var (
		QueryString string
	)
	client := h.Client.SetTimeout(30 * time.Second).R()

	for k, v := range Param {
		if QueryString == "" {
			QueryString = fmt.Sprintf("%s=%s", k, v)
		} else {
			QueryString = fmt.Sprintf("%s&%s=%s", QueryString, k, v)
		}
	}

	for k, v := range Header {
		client.SetHeader(k, v)
	}

	resp, err := client.SetQueryString(QueryString).Get(Url)
	//记录请求日志
	logger.E("GET请求记录", fmt.Sprintf("URL：%s，请求参数：%s，响应：%s，Status Code：%d，Time：%v", Url, QueryString, resp.String(), resp.StatusCode(), resp.Time()))

	return resp, err
}

func (h *HttpConfig) Post(Url string, Body interface{}, Header map[string]string) (*resty.Response, error) {
	client := h.Client.SetTimeout(30 * time.Second).R().SetBody(Body)

	for k, v := range Header {
		client.SetHeader(k, v)
	}

	resp, err := client.Post(Url)

	//记录请求日志
	logger.E("PSOT请求记录", fmt.Sprintf("URL：%s，请求参数：%v，响应：%s，Status Code：%d，Time：%v", Url, Body, resp.String(), resp.StatusCode(), resp.Time()))

	return resp, err
}

func (h *HttpConfig) PostJson(Url string, Body interface{}, Header map[string]string) (*resty.Response, error) {
	Byte, _ := json.Marshal(Body)
	client := h.Client.SetTimeout(30 * time.Second).R().SetBody(string(Byte))

	client.SetHeader("Content-Type", "application/json")

	for k, v := range Header {
		client.SetHeader(k, v)
	}

	resp, err := client.Post(Url)
	//记录请求日志
	logger.E("PSOT请求记录", fmt.Sprintf("URL：%s，请求参数：%v，响应：%s，Status Code：%d，Time：%v", Url, string(Byte), resp.String(), resp.StatusCode(), resp.Time()))

	return resp, err
}
