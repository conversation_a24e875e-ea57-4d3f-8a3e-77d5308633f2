// Code generated by goctl. DO NOT EDIT.
package handler

import (
	"net/http"

	data_analysis "engine/api/internal/handler/data_analysis"
	marketing "engine/api/internal/handler/marketing"
	second "engine/api/internal/handler/second"
	second_home "engine/api/internal/handler/second_home"
	"engine/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/get_product_label",
					Handler: second_home.GetProductLabelHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/get_full_reduction_label",
					Handler: second_home.GetFullReductionLabelHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/aggregation",
					Handler: second_home.AggregationHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/get_content",
					Handler: second_home.GetContentHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/aggregation_v2",
					Handler: second_home.AggregationV2Handler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/commodities_server/v3/second_home"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.User},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/filters/list",
					Handler: second.FiltersListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/filters/add",
					Handler: second.AddFiltersHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/filters/edit",
					Handler: second.EditFiltersHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/filters/update",
					Handler: second.UpdateFiltersHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/filters_goods/list",
					Handler: second.FiltersGoodsListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/filters_goods/add",
					Handler: second.AddFiltersGoodsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/filters_goods/edit",
					Handler: second.EditFiltersGoodsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/filters_goods/update",
					Handler: second.UpdateFiltersGoodsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/filters_goods/delete",
					Handler: second.DeleteFiltersGoodsHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/commodities_server/v3/second"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/marketing/filters_list",
					Handler: second.GetFiltersListHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/commodities_server/v3/second"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/OnlineGoodsRealtimeStatistics",
					Handler: data_analysis.OnlineGoodsRealtimeStatisticsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/ProductSaleFollowDatas",
					Handler: data_analysis.ProductSaleFollowDatasHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/GetPurchaseOrdersRealTimeData",
					Handler: data_analysis.GetPurchaseOrdersRealTimeDataHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/GetTailInventory",
					Handler: data_analysis.GetTailInventoryHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/GenerateTailInventory",
					Handler: data_analysis.GenerateTailInventoryHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/NewGeneratePurchaseOrder",
					Handler: data_analysis.NewGeneratePurchaseOrderHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/NewInventoryNotice",
					Handler: data_analysis.NewInventoryNoticeHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/OneFlowerOneWorldKanban",
					Handler: data_analysis.OneFlowerOneWorldKanbanHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/ExportOneFlowerOneWorldKanban",
					Handler: data_analysis.ExportOneFlowerOneWorldKanbanHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/commodities_server/v3/data_analysis"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/GetFullReductionActivityLabel",
					Handler: marketing.GetFullReductionActivityLabelHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/AutomaticallyAddPeriod",
					Handler: marketing.AutomaticallyAddPeriodHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/GetMarketingSection",
					Handler: marketing.GetMarketingSectionHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/SaleGoodsByRuleAddLabel",
					Handler: marketing.SaleGoodsByRuleAddLabelHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/AutomaticallyAddAllPeriod",
					Handler: marketing.AutomaticallyAddAllPeriodHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/commodities_server/v3/marketing"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method: http.MethodGet,
					Path:   "/",
					Handler: func(w http.ResponseWriter, r *http.Request) {
						w.Write([]byte("go-ok"))
					},
				},
			}...,
		),
	)
}
