package data_analysis

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common"
	"engine/common/esClient"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
)

type GetPurchaseOrdersRealTimeDataLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetPurchaseOrdersRealTimeDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPurchaseOrdersRealTimeDataLogic {
	return &GetPurchaseOrdersRealTimeDataLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetPurchaseOrdersRealTimeDataLogic) GetPurchaseOrdersRealTimeData(req *types.GetPurchaseOrdersRealTimeDataReq) (resp *types.GetPurchaseOrdersRealTimeDataResp, err error) {
	var result types.GetPurchaseOrdersRealTimeDataResp
	resp = &result
	result = types.GetPurchaseOrdersRealTimeDataResp{
		Periods:       make(map[int64]map[string]interface{}),
		Products:      make(map[string]map[string]interface{}),
		Supplier:      make(map[string]map[string]interface{}),
		PeriodProduct: make(map[string]map[string]interface{}),
		GrossMargin:   make(map[string]interface{}),
		SaleNum:       make(map[string]interface{}),
		UnshippedNum:  make(map[string]interface{}),
		MyInventory:   make(map[string]map[string]interface{}),
		Mystorage:     make(map[string]map[string]interface{}),
	}

	mr.Finish(func() (err error) {
		if !common.InArrayInt(req.QueryType, []int64{0, 1, 2}) {
			return
		}

		var (
			info        []map[string]interface{}
			query_param []map[string]interface{}
		)
		// 查询期数信息
		Es := esClient.New(l.svcCtx.Config)
		where := [][]interface{}{
			{"_id", "in", req.PeriodId},
		}
		Es.Name("periods").Where(where).Field([]string{
			"id",
			"estimate_purchase",
			"periods_type",
			"onsale_status",
			"onsale_time",
		}).Select(&info)
		for _, v := range info {
			//字符串转int64
			id, _ := strconv.ParseInt(fmt.Sprintf("%v", v["id"]), 10, 64)
			result.Periods[id] = v
			query_param = append(query_param, map[string]interface{}{
				"period":      v["id"],
				"period_type": v["periods_type"],
			})
		}

		if req.QueryType == 0 {
			mr.Finish(func() (err error) { // 查询销售瓶数
				var res_body GetSaleBottleNumsResp
				url_str := l.svcCtx.Config.ITEM.USER_CACHE_URL + "/commodities/GetSaleBottleNums"
				res, _ := l.svcCtx.HttpClient.PostJson(url_str, query_param, map[string]string{})
				json.Unmarshal(res.Body(), &res_body)
				result.SaleNum = res_body.Data
				return

			}, func() (err error) { //获取期数所有简码已售未发货数量
				var res_body GetSaleBottleNumsResp
				url_str := l.svcCtx.Config.ITEM.USER_CACHE_URL + "/commodities/GetUnshippedBottleNums"
				res, _ := l.svcCtx.HttpClient.PostJson(url_str, query_param, map[string]string{})
				json.Unmarshal(res.Body(), &res_body)
				result.UnshippedNum = res_body.Data
				return
			})
		}
		return
	}, func() (err error) {
		var info []map[string]interface{}
		if !common.InArrayInt(req.QueryType, []int64{0, 1, 2, 3}) {
			return
		}
		// 产品信息
		l.svcCtx.DbWiki.Table("vh_products as p").
			Joins("left join vh_product_unit_open pu on pu.id=p.product_unit").
			Where("p.short_code IN ?", req.ShortCode).
			Select("p.short_code,p.tax_rate,p.cn_product_name,p.en_product_name,p.capacity,pu.name as unit_name").
			Scan(&info)
		for _, v := range info {
			result.Products[v["short_code"].(string)] = v
		}
		return
	}, func() (err error) {
		var info []Supplier
		if !common.InArrayInt(req.QueryType, []int64{0, 1, 2, 3}) {
			return
		}
		// 供应商信息
		l.svcCtx.DbWiki.Table("vh_supplier").
			Where("supplier_name IN ?", req.SupplierName).
			Select("contract_end,supplier_tax,supplier_name").
			Scan(&info)
		for _, v := range info {
			result.Supplier[v.SupplierName] = map[string]interface{}{
				"contract_end":  v.ContractEnd.Format("2006-01-02"),
				"supplier_tax":  v.SupplierTax,
				"supplier_name": v.SupplierName,
			}
		}
		return
	}, func() (err error) {
		var info []map[string]interface{}
		if !common.InArrayInt(req.QueryType, []int64{0, 2}) {
			return
		}
		// 查询期数订货量
		l.svcCtx.DbCommodities.Table("vh_periods_product_inventory").
			Where("period IN ?", req.PeriodId).
			Select("`period`,`product_id`,`costprice`,`short_code`,`order`").
			Scan(&info)
		for _, v := range info {
			result.PeriodProduct[fmt.Sprintf("%v_%v", v["short_code"], v["period"])] = v
		}
		return
	}, func() (err error) {
		if !common.InArrayInt(req.QueryType, []int64{2}) {
			return
		}
		//批量查询期数毛利率
		var res_body GetSaleBottleNumsResp
		url_str := l.svcCtx.Config.ITEM.COMMODITIES_URL + "/commodities/v3/periods/BatchQueryPeriodGrossProfitMargin"
		query_param := map[string]interface{}{
			"period": req.PeriodId,
		}
		res, _ := l.svcCtx.HttpClient.PostJson(url_str, query_param, map[string]string{})
		json.Unmarshal(res.Body(), &res_body)
		result.GrossMargin = res_body.Data
		return
	}, func() (err error) {
		if !common.InArrayInt(req.QueryType, []int64{0, 1, 2}) {
			return
		}
		// 查询萌牙库存
		var res_body GoodsGetFictitiousCountResp
		query_param := map[string]interface{}{
			"short_code": req.ShortCode,
			"store_code": "xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa",
		}
		url_str := l.svcCtx.Config.ITEM.WMS_DISTRIBUTE_URL + "/query/goodsGetFictitiousCount"
		res, _ := l.svcCtx.HttpClient.PostJson(url_str, query_param, map[string]string{})
		json.Unmarshal(res.Body(), &res_body)
		for k, v := range res_body.Data {
			for _, vv := range v {
				if _, ok := result.MyInventory[k]; !ok {
					result.MyInventory[k] = map[string]interface{}{
						"fictitious_id": vv.FictitiousId,
						"goods_count":   int64(0),
						"transit_count": int64(0),
					}
				}
				result.MyInventory[k] = map[string]interface{}{
					"fictitious_id": vv.FictitiousId,
					"goods_count":   vv.GoodsCount + result.MyInventory[k]["goods_count"].(int64),
					"transit_count": vv.TransitCount + result.MyInventory[k]["transit_count"].(int64),
				}

				result.MyInventory[fmt.Sprintf("%v_%v", k, vv.FictitiousId)] = map[string]interface{}{
					"fictitious_id": vv.FictitiousId,
					"goods_count":   vv.GoodsCount,
					"transit_count": vv.TransitCount,
				}
			}
		}
		return
	}, func() (err error) {
		var info []Storage
		if len(req.PurchaseOrderNo) == 0 {
			return
		}
		l.svcCtx.DbWms.Model(&info).Where("rd_code IN ?", req.PurchaseOrderNo).Scan(&info)
		for _, v := range info {
			result.Mystorage[v.RdCode] = map[string]interface{}{
				"storage_id":       v.StorageId,
				"warehousing_code": v.WarehousingCode,
				"rd_code":          v.RdCode,
				"status":           v.Status,
			}
		}
		return
	})

	return
}
