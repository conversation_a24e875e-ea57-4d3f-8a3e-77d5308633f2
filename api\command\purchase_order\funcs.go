package purchase_order

import (
	"encoding/base64"
	"encoding/json"
	"engine/common"
	"engine/common/config"
	"engine/common/esClient"
	"engine/common/httpClient"
	"engine/common/logger"
	"engine/common/xtime"
	"fmt"
	"math"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/zeromicro/go-zero/core/mr"
	"gorm.io/gorm"
)

// 解析套餐产品
func (l *PurchaseOrderLogic) AnalysisPackageProducts(packages *[]PackageInfo) (pkg_info *map[int64]PackageInfo, prd_ids *[]int64) {
	var product_ids []int64
	// 期数套餐
	package_map := make(map[int64]PackageInfo)
	prd_ids = &product_ids
	pkg_info = &package_map
	// 解析套餐产品
	for _, pkg := range *packages {
		if pkg.AssociatedProducts != "" {
			var products []AssociatedProducts
			json.Unmarshal([]byte(pkg.AssociatedProducts), &products)
			if len(products) > 0 {
				for _, p := range products {
					product_id := make([]int64, 0)
					switch productid := p.ProductId.(type) {
					case []interface{}: //数组
						for _, subID := range productid {
							sub_id := fmt.Sprintf("%v", subID)
							if subid, _ := strconv.ParseInt(sub_id, 10, 64); subid > 0 {
								product_id = append(product_id, subid)
								if !common.InArrayInt(subid, product_ids) {
									product_ids = append(product_ids, subid)
								}
							}
						}
					default: //非数组
						sub_id := fmt.Sprintf("%v", productid)
						if subid, _ := strconv.ParseInt(sub_id, 10, 64); subid > 0 {
							product_id = append(product_id, subid)
							if !common.InArrayInt(subid, product_ids) {
								product_ids = append(product_ids, subid)
							}
						}
					}
					pkg.Products = append(pkg.Products, ProductData{
						ProductId: product_id,
						Nums:      p.Nums,
						IsGift:    p.IsGift,
					})
				}
			}
		}
		package_map[pkg.Id] = pkg
	}

	return
}

// 休息日处理方法，修改采购时间为下一个工作日
func (l *PurchaseOrderLogic) HolidayHandle(periods []EsPeriodsInfo, next_workday int64) {
	if next_workday == 0 {
		return
	}
	// 今日结束时间戳
	todayend := xtime.TodayEndTimeStamp()
	url_str := fmt.Sprintf("%s%s", l.Config.ITEM.COMMODITIES_URL, "/commodities/v3/purchase/updateEstimatePurchase")
	es := esClient.New(l.Config)
	for _, v := range periods {

		// 采购时间戳
		purchase_time := xtime.StrToTime(v.EstimatePurchase)
		// 采购时间大于今日23点59分59秒不修改
		if purchase_time > todayend {
			continue
		}

		// date日期修改为时间戳的日期
		dateString := xtime.FormatTime(v.EstimatePurchase, next_workday)
		if dateString == "" {
			continue
		}
		// 更新es采购时间为下一个工作日
		params := map[string]interface{}{
			"estimate_purchase": dateString,
		}
		logger.I(fmt.Sprintf("HolidayHandle estimate_purchase update ID:%d", v.Id), params)
		err := es.Name("periods").IDByUpdate(fmt.Sprintf("%d", v.Id), params)
		if err != nil {
			logger.I("HolidayHandle estimate_purchase update result", err)
			l.HttpClient.PostJson(url_str, map[string]interface{}{
				"period":            v.Id,
				"estimate_purchase": dateString,
			}, map[string]string{})
		}
		//100毫秒执行一次
		time.Sleep(time.Millisecond * 100)
	}
}

// 获取支持采购下单的期数
func (l *PurchaseOrderLogic) GetPeriods() []EsPeriodsInfo {
	var (
		result []EsPeriodsInfo
	)
	// 获取今日采购时间的期数，48小时以内、下架时间大于5天
	d_time := int64(time.Now().Unix())
	Es := esClient.New(l.Config)
	where := [][]interface{}{
		{"estimate_purchase", ">=", xtime.DateFormat("2006-01-02 00:00:00", d_time)},
		{"estimate_purchase", "<=", xtime.DateFormat("2006-01-02", d_time+86400) + " 23:59:59"},
		{"sold_out_time", ">=", xtime.DateFormat("2006-01-02", d_time-(12*86400)) + " 00:00:00"},
		{"periods_type", "in", []int64{0, 1, 2, 3}},
		{"import_type", "=", 1},           // 地采
		{"supplier", "not like", "*闪购仓*"}, // 供应商非闪购仓
		{"supplier", "not like", "*秒发仓*"}, // 供应商非秒发仓
	}

	Es.Name("periods").
		Where(where).
		Field([]string{
			"id",
			"supplier",
			"supplier_id",
			"buyer_name",
			"buyer_id",
			"estimate_purchase",
			"periods_type",
			"payee_merchant_id",
			"payee_merchant_name",
			"is_supplier_delivery",
		}).
		Select(&result)

	return result
}

// 判断今日是否为工作日（包含调休在内需要上班的日子）
func (l *PurchaseOrderLogic) getNextWorkday() map[string]int64 {
	var (
		info      HolidaysRes
		next_info NextWorkdayRes
	)
	// workday：-工作日，2-休息日。next_workday：下一个工作日时间戳
	result := map[string]int64{
		"workday":      1,
		"next_workday": 0,
	}

	// 今日日期
	date_str := xtime.DateFormat("2006-01-02", Dtime)

	// 读取缓存
	if v, ok := Workday[date_str]; ok {
		return v
	}

	// 查询今日是否假期
	url := l.Config.ITEM.OPENAPI_URL + "/openapi/v3/utils/holidays/check"
	body := map[string]string{"date": date_str}
	res, _ := httpClient.Handle(l.Config).Get(url, body, map[string]string{})
	if res.String() == "" {
		return result
	}
	json.Unmarshal(res.Body(), &info)
	if info.ErrorCode != 0 {
		return result
	}

	//是否为假期，true为假期。
	if info.IsHoliday {
		// 查询下一个工作日
		url = l.Config.ITEM.OPENAPI_URL + "/openapi/v3/utils/holidays/next_workday"
		body = map[string]string{"date": date_str}
		nextRes, _ := httpClient.Handle(l.Config).Get(url, body, map[string]string{})
		if nextRes.String() == "" {
			return result
		}

		json.Unmarshal(nextRes.Body(), &next_info)
		if next_info.NextWorkingDay == "" {
			return result
		}

		next_workday := xtime.StrToTimeFormat("2006-01-02", next_info.NextWorkingDay)
		result = map[string]int64{
			"workday":      2,
			"next_workday": next_workday,
		}
	}

	Workday = map[string]map[string]int64{
		date_str: result,
	}
	return result
}

// func (l *PurchaseOrderLogic) getNextWorkday_bak() map[string]int64 {
// 	var info CalendarRes
// 	// workday：-工作日，2-休息日。next_workday：下一个工作日时间戳
// 	result := map[string]int64{
// 		"workday":      1,
// 		"next_workday": 0,
// 	}
// 	date := xtime.DateFormat("20060102", Dtime)

// 	year := xtime.DateFormat("2006", Dtime)
// 	key := fmt.Sprintf("calendar.%s", year)

// 	// 查询今年日历缓存
// 	reply, _ := redis.String(l.Rdb.Invoke(func(conn redis.Conn) (interface{}, error) {
// 		return conn.Do("GET", key)
// 	}))

// 	// 查询今年日历
// 	if reply == "" {
// 		url := "https://api.apihubs.cn/holiday/get"
// 		body := map[string]string{
// 			"size": "500",
// 			"year": year,
// 		}
// 		res, _ := httpClient.Handle(l.Config).Get(url, body, map[string]string{})
// 		if res.String() != "" {
// 			reply = res.String()
// 			if reply != "" {
// 				// 保存缓存
// 				l.Rdb.Invoke(func(conn redis.Conn) (interface{}, error) {
// 					return conn.Do("SETEX", key, 86400*365, reply)
// 				})
// 			}
// 		}
// 	}

// 	if reply != "" { // 解析日历
// 		json.Unmarshal([]byte(reply), &info)
// 		if len(info.Data.List) > 0 {
// 			for _, v := range info.Data.List {
// 				if fmt.Sprintf("%d", v.Date) == date {
// 					result["workday"] = v.Workday
// 					if v.Workday == 2 {
// 						d_time := Dtime
// 						for i := 0; i < 500; i++ {
// 							// 获取明天时间戳
// 							d_time = d_time + 86400
// 							next_date := xtime.DateFormat("20060102", d_time)
// 							for _, vv := range info.Data.List {
// 								if fmt.Sprintf("%d", vv.Date) == next_date && vv.Workday == 1 {
// 									result["next_workday"] = xtime.StrToTimeFormat("20060102", fmt.Sprintf("%d", vv.Date))
// 									break
// 								}
// 							}
// 							if result["next_workday"] > 0 {
// 								break
// 							}
// 						}
// 					}
// 				}
// 			}
// 		}
// 	}

// 	return result
// }

/**
 * 收款公司id与code兑换
 * @param string $value 值
 * @return int|string
 */
func (l *PurchaseOrderLogic) PayeeMerchantIdCodeExchange(value int64) string {
	var result string

	switch value {
	case 1:
		result = "002" // 重庆云酒佰酿电子商务有限公司
	case 2:
		result = "029" // 佰酿云酒（重庆）科技有限公司
	case 5:
		result = "008" // 渝中区微醺酒业商行
	case 10:
		result = "032" // 海南一花一世界科技有限公司
	default:
		result = ""
	}

	return result
}

// 采购单号
func (l *PurchaseOrderLogic) GetPoNo(value int64) string {
	date := xtime.DateFormat("2006-01-02", Dtime)
	no := "PO-" + date + "-" + fmt.Sprintf("%d", value+1000) + "K"

	// 判断字符串中是否包含指定字符wineyun（测试环境）
	if strings.Contains(l.Config.ITEM.ALIURL, "wineyun") {
		no = no + "-TEST"
	}

	return no
}

// 查询采购单所需数据
func (l *PurchaseOrderLogic) QueryDatas(periods []EsPeriodsInfo) *PurchaseOrderNeedDatas {
	var (
		partner_entity []PartnerEntity
		staff          []Staff
		period_ids     []int64
		count          int64
		lj_type_id     []int64
		buyer_name     []string
		supplier_id    []int64
	)

	// 查询烈酒所有类型
	lj_type := l.GetProductType(22, []ProductType{}, 0)
	for _, v := range lj_type {
		lj_type_id = append(lj_type_id, v.Id)
	}

	// 期数信息
	period_info_map := make(map[int64]EsPeriodsInfo)
	// 根据频道区分期数
	period_id_map := make(map[int64][]int64)
	// 往来单位数据
	partner_entity_map := make(map[string]PartnerEntity)
	// 磐石供应商数据
	supplier_map := make(map[int64]Supplier)
	// 员工数据
	staff_map := make(map[string]Staff)
	// 已创建采购单期数
	purchase_order_period := make(map[string]PeriodsProductInventory)
	// 期数订单数据
	orders_map := make(map[int64][]SubOrders)
	// 期数套餐数据
	package_map := make(map[int64]PackageInfo)
	// 产品数据
	product_map := make(map[string]PeriodsProductInfo)
	// 今日已下单期数
	exist_period := make(map[int64]int64)
	// 萌牙库存数据
	wms_stock_map := sync.Map{}
	wms_stock_datas := make(map[string]WmsGoodsCountData)
	// 采购备货列表
	prepare_purchase_map := sync.Map{}
	prepare_purchase_datas := make(map[string]PreparePurchaseList)

	// 期数取值
	for _, v := range periods {
		period_ids = append(period_ids, v.Id)
		period_info_map[v.Id] = v
		if _, ok := period_id_map[v.PeriodsType]; !ok {
			period_id_map[v.PeriodsType] = []int64{v.Id}
		} else {
			period_id_map[v.PeriodsType] = append(period_id_map[v.PeriodsType], v.Id)
		}
		supplier_id = append(supplier_id, v.SupplierId)
		buyer_name = append(buyer_name, v.BuyerName)
	}

	mr.Finish(func() (err error) {
		var (
			info          []Supplier
			supplier_name []string
		)
		// 查询磐石供应商信息
		l.Db.Table("`vh_wiki`.`vh_supplier`").
			Where("id IN ?", supplier_id).
			Select("id,supplier_name,supplier_tax").
			Scan(&info)
		for _, v := range info {
			supplier_map[v.Id] = v
			supplier_name = append(supplier_name, v.SupplierName)
		}

		// 查询往来单位信息
		l.Db.Table("`vh_supplychain`.`vh_partner_entity` e").
			Joins("left join vh_supplychain.vh_department d on d.id=e.purchase_department_id").
			Joins("left join vh_supplychain.vh_staff s on s.id=e.purchase_maintainer_id").
			Where("e.name IN ?", supplier_name).
			Select("e.id,e.code,e.name,e.remarks,e.corp,d.dept_code,d.name as dept_name,s.staff_code,s.realname,e.memo").
			Scan(&partner_entity)
		for _, v := range partner_entity {
			partner_entity_map[v.Name] = v
		}

		return
	}, func() (err error) {
		// 员工信息
		l.Db.Table("`vh_supplychain`.`vh_staff` s").
			Joins("left join vh_supplychain.vh_department d on d.id=s.dept_id").
			Where("realname IN ?", buyer_name).
			Select("s.id,s.staff_code,s.realname,d.dept_code,d.name as dept_name").
			Scan(&staff)
		for _, v := range staff {
			staff_map[v.Realname] = v
		}
		return
	}, func() (err error) {
		var info []PurchaseOrdernoPeriod
		// 查询今日已下单期数
		l.Db.Model(&info).
			Where("period IN ? and created_time >= ?", period_ids, TodayTimestamp).
			Scan(&info)
		for _, v := range info {
			exist_period[v.Period] = v.Id
		}
		return
	}, func() (err error) {
		var info []PeriodsProductInventory
		// 查询期数所有产品
		l.Db.Model(&info).
			Where("period IN ?", period_ids).
			Scan(&info)
		for _, v := range info {
			key := fmt.Sprintf("%d%d", v.Period, v.ProductId)
			// 当前期数所有产品
			purchase_order_period[key] = v
		}
		return
	}, func() (err error) {
		var wg sync.WaitGroup
		// 查询订单信息
		for ty, v := range period_id_map {
			wg.Add(1)
			go func(ty int64, v []int64) {
				defer wg.Done()
				var (
					order        []SubOrders
					package_ids  []int64
					packages     []PackageInfo
					product_ids  []int64
					product_info []PeriodsProductInfo
					short_code   []string
				)
				// 订单表
				order_table := config.OrderTable[ty]
				// 套餐
				package_table := config.PackageTable[ty]

				// 查询订单信息
				order_query := l.OrderDb.Table(order_table).
					Where("period IN ? and sub_order_status in(1,2,3) and special_type <> 4", v)

				if ty == 2 {
					order_query.Select("id,sub_order_no,sub_order_status,period,package_id,order_qty")
				} else {
					order_query.Select("id,sub_order_no,sub_order_status,period,package_id,order_qty,push_wms_status")
				}
				order_query.Scan(&order)
				// 获取套餐ID
				for _, o := range order {
					orders_map[o.Period] = append(orders_map[o.Period], o)
					package_ids = append(package_ids, o.PackageId)
				}
				// 查询套餐信息
				l.Db.Table(package_table).Where("id IN ?", package_ids).
					Select("id,period_id,associated_products").
					Scan(&packages)

				// 解析套餐产品
				pkg_info, prd_ids := l.AnalysisPackageProducts(&packages)
				for key, pkg := range *pkg_info {
					package_map[key] = pkg
				}
				product_ids = *prd_ids
				// 查询产品信息
				l.Db.Table("vh_periods_product_inventory i").
					Joins("left join vh_wiki.vh_products p on i.product_id=p.id").
					Joins("left join vh_wiki.vh_product_unit u on u.id=p.product_unit").
					Joins("left join vh_wiki.vh_product_type t on t.id=p.product_type").
					Where("i.product_id IN ?", product_ids).
					Select("i.id,i.period,i.periods_type,i.product_id,i.costprice,p.bar_code,p.short_code,i.warehouse,i.warehouse_id,i.erp_id,p.cn_product_name,p.en_product_name,p.capacity,u.name as product_unit_name,p.tax_rate,p.product_type,t.fid,p.invoice_name").
					Scan(&product_info)
				for _, p := range product_info {
					short_code = append(short_code, p.ShortCode)
					key := fmt.Sprintf("%d%d", p.Period, p.ProductId)
					product_map[key] = p
					// 判断烈酒
					if common.InArrayInt(p.ProductType, lj_type_id) {
						if vv, ok := period_info_map[p.Period]; ok {
							vv.IsLiejue = 1
							period_info_map[p.Period] = vv
						}
					}
				}

				mr.Finish(func() (err error) {
					var wms_res WmsGoodsCountResp
					// 查询萌牙库存
					url_str := l.Config.ITEM.WMS_DISTRIBUTE_URL + "/query/goodsGetFictitiousCount"
					body := map[string]interface{}{
						"short_code": short_code,
						"store_code": "xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa",
					}
					res, _ := l.HttpClient.PostJson(url_str, body, map[string]string{})
					json.Unmarshal(res.Body(), &wms_res)
					for k, v := range wms_res.Data {
						for _, vv := range v {
							key := fmt.Sprintf("%s_%d", k, vv.FictitiousId)
							wms_stock_map.Store(key, vv)
						}
					}
					return
				}, func() (err error) {
					var info PreparePurchaseListResp
					// 查询备货列表
					url_str := l.Config.ITEM.PURCHASE_MANAGEMENT + "/purchase/v3/PreparePurchase/list"
					// 切片转字符串
					short_code_str := strings.Join(short_code, ",")
					body := map[string]string{
						"short_code": short_code_str,
						"limit":      "10000",
						"page":       "1",
					}
					res, _ := l.HttpClient.PostJson(url_str, body, map[string]string{})
					json.Unmarshal(res.Body(), &info)
					for _, v := range info.Data.List {
						key := fmt.Sprintf("%s_%s", v.ShortCode, v.CustomerName)
						prepare_purchase_map.Store(key, v)
					}
					return
				})

			}(ty, v)
		}
		wg.Wait()
		return
	}, func() (err error) {
		//查询今日创建订单量
		l.Db.Model(&PurchaseOrderno{}).
			Where("source = 2 and created_time > ?", TodayTimestamp).
			Count(&count)
		return
	})

	// 萌牙库存数据
	wms_stock_map.Range(func(key, value interface{}) bool {
		wms_stock_datas[key.(string)] = value.(WmsGoodsCountData)
		return true
	})

	// 备货数据
	prepare_purchase_map.Range(func(key, value interface{}) bool {
		prepare_purchase_datas[key.(string)] = value.(PreparePurchaseList)
		return true
	})

	return &PurchaseOrderNeedDatas{
		PeriodInfoMap:        period_info_map,
		PartnerEntityMap:     partner_entity_map,
		PurchaseOrderPeriod:  purchase_order_period,
		OrdersMap:            orders_map,
		PackageMap:           package_map,
		ProductMap:           product_map,
		ExistPeriod:          exist_period,
		PeriodIds:            period_ids,
		Count:                count,
		WmsStockDatas:        wms_stock_datas,
		StaffMap:             staff_map,
		SupplierMap:          supplier_map,
		PreparePurchaseDatas: prepare_purchase_datas,
	}
}

// 修改期数下次采购时间
func (l *PurchaseOrderLogic) UpdatePurchaseTime(periods []EsPeriodsInfo, periods_map map[int64]EsPeriodsInfo) {
	url_str := fmt.Sprintf("%s%s", l.Config.ITEM.COMMODITIES_URL, "/commodities/v3/purchase/updateEstimatePurchase")
	es := esClient.New(l.Config)
	// 今日结束时间戳
	todayend := xtime.TodayEndTimeStamp()

	// 修改采购时间+48小时
	for _, v := range periods {
		fmt.Println(v.Id)
		var estimate_purchase interface{}

		info := periods_map[v.Id]
		// 采购时间戳
		purchase_time := xtime.StrToTime(v.EstimatePurchase)
		// 采购时间大于今日23点59分59秒不修改
		if purchase_time > todayend {
			continue
		}
		// 代发 无下次采购时间
		if info.IsSupplierDelivery == 1 {
			estimate_purchase = nil

		} else { // 非代发
			estimate_purchase = xtime.Date(purchase_time + (3600 * 48))
			// 烈酒 采购时间为+24*4小时
			// if info.IsLiejue == 1 {
			// 	estimate_purchase = xtime.Date(purchase_time + (3600 * 24 * 4))

			// } else { // 非烈酒 采购时间为+48小时
			// 	estimate_purchase = xtime.Date(purchase_time + (3600 * 48))
			// }
		}

		// 更新es采购时间
		params := map[string]interface{}{
			"estimate_purchase": estimate_purchase,
		}
		logger.I(fmt.Sprintf("estimate_purchase update ID:%d", v.Id), params)
		err := es.Name("periods").IDByUpdate(fmt.Sprintf("%d", v.Id), params)
		if err != nil {
			logger.I("estimate_purchase update result", err)
			l.HttpClient.PostJson(url_str, map[string]interface{}{
				"period":            v.Id,
				"estimate_purchase": estimate_purchase,
			}, map[string]string{})
		}
		//100毫秒执行一次
		time.Sleep(time.Millisecond * 100)

	}
}

// 非代发，非烈酒，首次采购瓶数=实际瓶数*1.2倍且需要是6的倍数
func (l *PurchaseOrderLogic) FirstQuantityCalculation(number int64) int64 {
	number = int64(math.Ceil(float64(number) * 1.2))
	// for i := 1; i <= 6; i++ {
	// 	if number%6 == 0 {
	// 		break
	// 	}
	// 	number++
	// }
	return number
}

// 查询产品类型（包括所有下级）
func (l *PurchaseOrderLogic) GetProductType(id int64, productType []ProductType, fid int64) []ProductType {
	if len(productType) == 0 {
		l.Db.Table("vh_wiki.vh_product_type").Scan(&productType)
	}
	var list []ProductType
	for _, v := range productType {
		if fid == 0 && v.Id == id {
			sList := l.GetProductType(id, productType, id)
			list = append(list, v)
			list = append(list, sList...)

		} else if fid > 0 && v.Fid == fid {
			sList := l.GetProductType(id, productType, v.Id)
			list = append(list, v)
			list = append(list, sList...)
		}
	}

	return list
}

// 修改期数下次采购时间
func (l *PurchaseOrderLogic) UpdateBeihuoData(use_beihuo_data *[]UseBeiHuoData) {
	beihuo_data := *use_beihuo_data
	if len(beihuo_data) == 0 {
		return
	}
	url_str := l.Config.ITEM.PURCHASE_MANAGEMENT + "/purchase/v3/PreparePurchase/update"

	for _, v := range beihuo_data {
		go func(v UseBeiHuoData) {
			err := l.Db.Transaction(func(tx *gorm.DB) error {
				// 增加采购数量
				err := tx.Table("vh_periods_product_inventory").
					Where("period = ? and short_code = ?", v.Period, v.ShortCode).
					UpdateColumn("order", gorm.Expr("`order` + ?", v.UseNums)).Error
				if err != nil {
					fmt.Println("vh_periods_product_inventory", err)
					return err
				}

				// 增加商品备注
				err = tx.Table("vh_periods_remark").Create(map[string]interface{}{
					"period":        v.Period,
					"periods_type":  v.PeriodsType,
					"remark":        fmt.Sprintf("【%s】已订货：%d，备货转已采：%d", v.ShortCode, v.UseNums, v.UseNums),
					"operator":      0,
					"operator_name": "系统生成采购单",
					"created_time":  time.Now().Unix(),
				}).Error
				if err != nil {
					fmt.Println("vh_periods_remark", err)
					return err
				}
				return nil
			})
			if err != nil {
				logger.E("UpdateBeihuoData Error", err, v)
				return
			}

			// 扣减备货数量
			body := map[string]interface{}{
				"id":     v.BeiHuoId,
				"number": 0 - v.UseNums,
			}
			// 转base64
			vos_name := base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("期数：%d", v.Period)))
			l.HttpClient.PostJson(url_str, body, map[string]string{
				"vinehoo-uid":      "0",
				"vinehoo-vos-name": vos_name,
			})
		}(v)
	}
}

// 判断是否字符串中最大的期数
func (l *PurchaseOrderLogic) IsMaxPeriod(period_str string, period int64) bool {
	// 字符串转数组
	period_list := strings.Split(period_str, ",")
	for _, v := range period_list {
		// 转换为int64
		period_int, _ := strconv.ParseInt(v, 10, 64)
		// 如果当前期数大于最大期数，则返回true
		if period_int > period {
			return false
		}
	}
	return true
}
