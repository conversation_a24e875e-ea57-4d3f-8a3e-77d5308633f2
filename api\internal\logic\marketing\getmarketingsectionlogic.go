package marketing

import (
	"context"

	"engine/api/internal/service/MarketingAutoAdd"
	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetMarketingSectionLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetMarketingSectionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMarketingSectionLogic {
	return &GetMarketingSectionLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetMarketingSectionLogic) GetMarketingSection(req *types.GetMarketingSectionReq) (resp *types.GetMarketingSectionResp, err error) {
	result := types.GetMarketingSectionResp{
		Card:   make([]types.MarketingSection, 0),
		Column: make([]types.MarketingSection, 0),
	}
	resp = &result

	service := MarketingAutoAdd.NewMarketingAutoAddLogic(l.svcCtx)

	// 执行添加操作逻辑
	section := service.GetMarketingSection(req.Period, req.PeriodType, 0)

	// 卡片
	for _, v := range section.Card {
		filter := make([]types.MarketingSectionFilter, 0)
		for _, v1 := range v.Filter {
			filter = append(filter, types.MarketingSectionFilter{
				Id:   v1.Id,
				Name: v1.Name,
			})
		}

		result.Card = append(result.Card, types.MarketingSection{
			Id:     v.Id,
			Name:   v.CardName,
			Filter: filter,
		})
	}
	// 栏目
	for _, v := range section.Column {
		filter := make([]types.MarketingSectionFilter, 0)
		for _, v1 := range v.Filter {
			filter = append(filter, types.MarketingSectionFilter{
				Id:   v1.Id,
				Name: v1.Name,
			})
		}
		result.Column = append(result.Column, types.MarketingSection{
			Id:     v.Id,
			Name:   v.Name,
			Filter: filter,
		})
	}

	return
}
