package second

import (
	"context"
	"sync"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type EditFiltersGoodsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewEditFiltersGoodsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *EditFiltersGoodsLogic {
	return &EditFiltersGoodsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *EditFiltersGoodsLogic) EditFiltersGoods(req *types.EditFiltersGoodsReq) error {
	var (
		filters_info PeriodsSecondFilters
		goods_count  int64
		table_name   string
		periods_info PeriodsData
		wg           sync.WaitGroup
		goods_info   PeriodsSecondFiltersGoods
	)
	if req.PeriodsType != 1 {
		return xerr.NewParamErrMsg("只能添加秒发商品")
	}
	// 操作人ID
	uid := l.ctx.Value("uid").(int64)
	// 操作人
	user_name := l.ctx.Value("name").(string)
	wg.Add(1)
	go func() {
		defer wg.Done()
		// 查询标签信息
		l.svcCtx.DbCommodities.Model(&filters_info).Where("id = ?", req.FiltersId).Take(&filters_info)
	}()
	wg.Add(1)
	go func() {
		defer wg.Done()
		// 查询商品数量
		l.svcCtx.DbCommodities.
			Model(&PeriodsSecondFiltersGoods{}).
			Where("id <> ? and filters_id = ? and periods = ?", req.Id, req.FiltersId, req.Periods).
			Count(&goods_count)
	}()
	wg.Add(1)
	go func() {
		defer wg.Done()
		// 查询商品信息
		l.svcCtx.DbCommodities.Model(&goods_info).Where("id = ? ", req.Id).Take(&goods_info)
	}()
	wg.Add(1)
	go func() {
		defer wg.Done()
		// 查询期数信息
		switch req.PeriodsType {
		case 0: // 闪购
			table_name = "vh_periods_flash"
		case 1: // 秒发
			table_name = "vh_periods_second"
		}
		l.svcCtx.DbCommodities.
			Table(table_name).
			Where("id = ?", req.Periods).
			Select("id", "onsale_status").
			Scan(&periods_info)
	}()
	wg.Wait()
	if goods_info.Id == 0 {
		return xerr.NewParamErrMsg("商品记录不存在请先添加")
	}
	if filters_info.Id == 0 {
		return xerr.NewParamErrMsg("筛选标签不存在")
	}
	if goods_count > 0 {
		return xerr.NewParamErrMsg("期数已添加")
	}
	if periods_info.Id == 0 {
		return xerr.NewParamErrMsg("期数不存在")
	}
	if periods_info.OnsaleStatus != 2 && req.Status == 1 {
		return xerr.NewParamErrMsg("只有在售中期数可显示")
	}

	goods := map[string]interface{}{
		"periods":          req.Periods,
		"periods_type":     req.PeriodsType,
		"goods_short_name": req.GoodsShortName,
		"status":           req.Status,
		"weight_value":     req.WeightValue,
		"update_id":        uid,
		"update_name":      user_name,
		"update_time":      int64(time.Now().Unix()),
	}
	err := l.svcCtx.DbCommodities.Model(&PeriodsSecondFiltersGoods{}).Where("id = ?", req.Id).Updates(goods).Error
	if err != nil {
		return xerr.NewParamErrMsg(err.Error())
	}

	if req.Status == 0 {
		// 自动判断筛选标签失效
		go AutomaticJudgmentFiltersFailure(l.svcCtx.DbCommodities, goods_info.FiltersId)
	}

	return nil
}
