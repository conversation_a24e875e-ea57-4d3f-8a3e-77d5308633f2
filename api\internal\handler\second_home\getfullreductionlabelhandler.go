package second_home

import (
	"engine/api/internal/logic/second_home"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
	"net/http"
)

func GetFullReductionLabelHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetFullReductionLabelReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := second_home.NewGetFullReductionLabelLogic(r.Context(), svcCtx)
		resp, err := l.GetFullReductionLabel(&req)
		result.HttpResult(r, w, resp, err)
	}
}
