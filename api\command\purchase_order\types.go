package purchase_order

type EsPeriodsInfo struct {
	Id                 int64  `json:"id"`
	Supplier           string `json:"supplier"`
	SupplierId         int64  `json:"supplier_id"`
	BuyerName          string `json:"buyer_name"`
	BuyerId            int64  `json:"buyer_id"`
	EstimatePurchase   string `json:"estimate_purchase"`
	PeriodsType        int64  `json:"periods_type"`
	PayeeMerchantId    int64  `json:"payee_merchant_id"`
	PayeeMerchantName  string `json:"payee_merchant_name"`
	IsSupplierDelivery int64  `json:"is_supplier_delivery"`
	IsLiejue           int64
}

type HolidaysRes struct {
	ErrorCode int64 `json:"error_code"`
	IsHoliday bool  `json:"is_holiday"`
}

type NextWorkdayRes struct {
	NextWorkingDay string `json:"next_working_day"`
}

type CalendarRes struct {
	Data struct {
		List []CalendarResList `json:"list"`
	} `json:"data"`
}

type CalendarResList struct {
	Date    int64 `json:"date"`
	Workday int64 `json:"workday"`
}

type SubOrders struct {
	Id             int64 `gorm:"primaryKey"`
	SubOrderNo     string
	SubOrderStatus int64
	Period         int64
	PackageId      int64
	OrderQty       int64
	PushWmsStatus  int64
}

type PackageInfo struct {
	Id                 int64 `gorm:"primaryKey"`
	PeriodId           int64
	AssociatedProducts string
	Products           []ProductData `gorm:"-"`
}

type ProductData struct {
	ProductId []int64
	Nums      int64
	IsGift    int64
}

type AssociatedProducts struct {
	ProductId interface{} `json:"product_id"`
	Nums      int64       `json:"nums"`
	IsGift    int64       `json:"isGift"`
}

type PeriodsProductInfo struct {
	Id              int64
	Period          int64
	PeriodsType     int64
	ProductId       int64
	Costprice       float64
	BarCode         string
	ShortCode       string
	Warehouse       string
	WarehouseId     string
	ErpId           string
	CnProductName   string
	EnProductName   string
	Capacity        string
	ProductUnitName string
	TaxRate         float64
	ProductType     int64
	Fid             int64
	InvoiceName     string
}

type PartnerEntity struct {
	Id        int64 `gorm:"primaryKey"`
	Code      string
	Name      string
	Remarks   string
	Corp      string
	DeptCode  string
	DeptName  string
	StaffCode string
	Realname  string
	Memo      string
}

type Staff struct {
	Id        int64
	StaffCode string
	Realname  string
	DeptCode  string
	DeptName  string
}

type PurchaseOrdernoPeriod struct {
	Id                 int64 `gorm:"primaryKey"`
	PurchaseOrdernoId  int64
	Period             int64
	PeriodsType        int64
	ShortCode          string
	Number             int64
	SaleNums           int64
	PurchasedNums      int64
	IsGift             int64
	CreatedTime        int64
	IsSupplierDelivery int64
}

type PurchaseOrderno struct {
	Id                int64 `gorm:"primaryKey"`
	Orderno           string
	Operator          int64
	OperatorName      string
	CreatedTime       int64
	Period            string
	UpdateTime        int64
	Warehouse         string
	WarehouseCode     string
	Supplier          string
	SupplierCode      string
	OperatorCode      string
	Department        string
	DepartmentCode    string
	CreatedUid        int64
	CreatedName       string
	Setttlement       string
	SetttlementCode   string
	Remark            string
	CorpCode          string
	PayeeMerchantId   int64
	PayeeMerchantName string
	BillDate          int64
	Source            int64
}

type PurchaseOrdernoItems struct {
	PurchaseOrdernoId  int64
	ShortCode          string
	BillingName        string
	EnProductName      string
	Unit               string
	Capacity           string
	Remark             string
	Period             string
	Number             string
	Price              float64
	TaxRate            float64
	IsGift             int64
	Total              float64
	SaleNums           int64
	UnshippedNum       int64
	PurchasedNums      int64
	IsFirst            int64
	IsSupplierDelivery int64
}

type PurchaseOrderNeedDatas struct {
	PeriodInfoMap        map[int64]EsPeriodsInfo
	PartnerEntityMap     map[string]PartnerEntity
	PurchaseOrderPeriod  map[string]PeriodsProductInventory
	OrdersMap            map[int64][]SubOrders
	PackageMap           map[int64]PackageInfo
	ProductMap           map[string]PeriodsProductInfo
	ExistPeriod          map[int64]int64
	PeriodIds            []int64
	Count                int64
	WmsStockDatas        map[string]WmsGoodsCountData
	StaffMap             map[string]Staff
	SupplierMap          map[int64]Supplier
	PreparePurchaseDatas map[string]PreparePurchaseList
}

type UnitRemarks struct {
	Zqkz     string `json:"zqkz"`
	Contacts struct {
		Phone        string `json:"phone"`
		AreaCode     string `json:"area_code"`
		ContactsName string `json:"contacts_name"`
	} `json:"contacts"`
	MaintainerName string `json:"maintainer_name"`
	SettlementName string `json:"settlement_name"`
}

type PeriodsProductInventory struct {
	Id          int64 `gorm:"primaryKey"`
	Period      int64
	PeriodsType int64
	ProductId   int64
	ShortCode   string
	Order       int64
}

type WmsGoodsCountResp struct {
	Data map[string][]WmsGoodsCountData `json:"data"`
}

type WmsGoodsCountData struct {
	BarCode      string `json:"bar_code"`
	GoodsCount   int64  `json:"goods_count"`
	TransitCount int64  `json:"transit_count"`
	FictitiousId int64  `json:"fictitious_id"`
}

type ProductType struct {
	Id   int64 `gorm:"primaryKey"`
	Name string
	Fid  int64
}

type ShipOrder struct {
	Id      int64 `gorm:"primaryKey"`
	Orderno string
}

type Supplier struct {
	Id           int64 `gorm:"primaryKey"`
	SupplierName string
	SupplierTax  string
}

type PreparePurchaseListResp struct {
	Data struct {
		List []PreparePurchaseList `json:"list"`
	} `json:"data"`
}
type PreparePurchaseList struct {
	Id           int64  `json:"id"`
	ShortCode    string `json:"short_code"`
	CustomerName string `json:"customer_name"`
	Number       int64  `json:"number"`
}

type UseBeiHuoData struct {
	Period      int64
	PeriodsType int64
	ShortCode   string
	BeiHuoId    int64
	UseNums     int64
}
