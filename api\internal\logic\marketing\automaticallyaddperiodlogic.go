package marketing

import (
	"context"

	"engine/api/internal/service/MarketingAutoAdd"
	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AutomaticallyAddPeriodLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAutomaticallyAddPeriodLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AutomaticallyAddPeriodLogic {
	return &AutomaticallyAddPeriodLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AutomaticallyAddPeriodLogic) AutomaticallyAddPeriod(req *types.AutomaticallyAddPeriodReq) error {
	service := MarketingAutoAdd.NewMarketingAutoAddLogic(l.svcCtx)

	// 执行添加操作逻辑
	go service.MarketingSectionAddPeriod(req, 0)

	return nil
}
