package marketing

import (
	"context"
	"time"

	"engine/api/internal/service/MarketingAutoAdd"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/config"

	"github.com/zeromicro/go-zero/core/logx"
)

type SaleGoodsByRuleAddLabelLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSaleGoodsByRuleAddLabelLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SaleGoodsByRuleAddLabelLogic {
	return &SaleGoodsByRuleAddLabelLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SaleGoodsByRuleAddLabelLogic) SaleGoodsByRuleAddLabel(req *types.SaleGoodsByRuleAddLabelReq) error {
	go func() {
		for k, v := range config.PeriodTable {
			var info []CommonInfo
			l.svcCtx.DbCommodities.Table(v).
				Where("onsale_status = 2 and is_channel = 0").
				Select("id").Scan(&info)
			for _, vv := range info {
				service := MarketingAutoAdd.NewMarketingAutoAddLogic(l.svcCtx)
				body := &types.AutomaticallyAddPeriodReq{
					Period:     vv.Id,
					PeriodType: int64(k),
					Label:      []int64{req.LabelId},
				}
				service.MarketingSectionAddPeriod(body, 1)

				// 休眠100毫秒
				time.Sleep(100 * time.Millisecond)
			}
		}
	}()

	return nil
}
