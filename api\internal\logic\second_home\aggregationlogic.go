package second_home

import (
	"context"
	"encoding/json"
	"fmt"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
)

type AggregationLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAggregationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AggregationLogic {
	return &AggregationLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

type SecondHome struct {
	Data struct {
		List []map[string]interface{} `json:"list"`
	} `json:"data"`
}

type UserCollectTags struct {
	Data struct {
		Id      int64                    `json:"id"`
		Name    string                   `json:"name"`
		Subhead string                   `json:"subhead"`
		Labels  []map[string]interface{} `json:"labels"`
	} `json:"data"`
}
type PeriodsSecondFiltersRes struct {
	Data struct {
		List []PeriodsSecondFiltersList `json:"list"`
	} `json:"data"`
}
type PeriodsSecondFiltersList struct {
	Title      string
	Identifier string
}

func (l *AggregationLogic) Aggregation(req *types.AggregationReq) (resp *types.AggregationResp, err error) {
	var newusers_activity map[string]interface{}
	home_screen := make([]map[string]interface{}, 0)
	collect_tags := make(map[string]interface{})
	hot_topic := make([]map[string]interface{}, 0)
	banner := make([]map[string]interface{}, 0)
	second_filters_list := make([]map[string]interface{}, 0)

	mr.Finish(func() (err error) {
		//秒发筛选列表 https://showdoc.wineyun.com/web/#/24/2988
		home_screen = *l.GetSecondScreen(req)
		return
	}, func() (err error) {
		//用户采集标签 https://showdoc.wineyun.com/web/#/25/4380
		collect_tags = *l.GetUserCollectTags(req)
		return
	}, func() (err error) {
		//热门话题列表 https://showdoc.wineyun.com/web/#/40/1981
		hot_topic = *l.GetHotTopic(req)
		return
	}, func() (err error) {
		//banner列表 https://showdoc.wineyun.com/web/#/24/2628
		banner = *l.GetBanner(req)
		return
	}, func() (err error) {
		//获取新人活动 https://showdoc.wineyun.com/web/#/37/4425
		newusers_activity = *l.GetNewusersActivity(req)
		return
	}, func() (err error) {
		//获取秒发二级筛选列表
		second_filters_list = *l.GetSecondFiltersList(req)
		return
	})
	result := types.AggregationResp{
		HomeScreen:        home_screen,
		CollectTags:       collect_tags,
		HotTopic:          hot_topic,
		Banner:            banner,
		NewusersActivity:  newusers_activity,
		SecondFiltersList: second_filters_list,
	}

	return &result, nil
}

// 获取秒发二级筛选列表
func (l *AggregationLogic) GetSecondFiltersList(req *types.AggregationReq) *[]map[string]interface{} {
	var data PeriodsSecondFiltersRes
	result := make([]map[string]interface{}, 0)
	url := l.svcCtx.Config.ITEM.COMMODITIES_SERVICE_URL + "/commodities_server/v3/second/marketing/filters_list"
	body := map[string]string{
		"longitude": req.Longitude,
		"latitude":  req.Latitude,
	}
	header := map[string]string{}
	res, _ := l.svcCtx.HttpClient.Get(url, body, header)
	json.Unmarshal(res.Body(), &data)

	if len(data.Data.List) > 0 {
		for _, v := range data.Data.List {
			result = append(result, map[string]interface{}{
				"title":      v.Title,
				"identifier": v.Identifier,
			})
		}
	}
	return &result
}

// 秒发筛选列表
func (l *AggregationLogic) GetSecondScreen(req *types.AggregationReq) *[]map[string]interface{} {
	var (
		header    map[string]string
		home_data SecondHome
	)
	// result := make([]map[string]interface{}, 0)
	// uid := l.ctx.Value("uid").(int64)
	// name := l.ctx.Value("vos_name").(string)
	// url := l.svcCtx.Config.ITEM.ACTIVITIES_MANAGEMENT_URL + "/activity/v3/second/getList"
	// body := make(map[string]string)
	// if req.Mid != "" {
	// 	body["mid"] = req.Mid
	// }
	// if uid > 0 {
	// 	header = map[string]string{
	// 		"vinehoo-uid":      fmt.Sprintf("%d", uid),
	// 		"vinehoo-vos-name": name,
	// 	}
	// }
	// res, err := l.svcCtx.HttpClient.Get(url, body, header)
	// if err != nil {
	// 	return &result
	// }
	// json.Unmarshal(res.Body(), &home_data)

	// result = home_data.Data.List

	result := make([]map[string]interface{}, 0)
	url := l.svcCtx.Config.ITEM.COMMODITIES_URL + "/commodities/v3/second/getSecondTopFilter"
	body := make(map[string]string)
	res, err := l.svcCtx.HttpClient.Get(url, body, header)
	if err != nil {
		return &result
	}
	json.Unmarshal(res.Body(), &home_data)

	result = home_data.Data.List
	return &result
}

// 获取用户采集标签
func (l *AggregationLogic) GetUserCollectTags(req *types.AggregationReq) *map[string]interface{} {
	var (
		header       map[string]string
		collect_tags UserCollectTags
	)
	result := make(map[string]interface{})
	uid := l.ctx.Value("uid").(int64)
	name := l.ctx.Value("vos_name").(string)

	if uid > 0 {
		url := l.svcCtx.Config.ITEM.COMMODITIES_URL + "/commodities/v3/userPortrait/activeUserPortrait"
		body := make(map[string]string)
		header = map[string]string{
			"vinehoo-uid":      fmt.Sprintf("%d", uid),
			"vinehoo-vos-name": name,
		}
		res, err := l.svcCtx.HttpClient.Get(url, body, header)
		if err != nil {
			return &result
		}
		json.Unmarshal(res.Body(), &collect_tags)
		if collect_tags.Data.Id > 0 && len(collect_tags.Data.Labels) > 0 {
			if collect_tags.Data.Subhead == "" {
				collect_tags.Data.Subhead = "为您提供更精准的推荐"
			}
			result = map[string]interface{}{
				"id":      collect_tags.Data.Id,
				"name":    collect_tags.Data.Name,
				"subhead": collect_tags.Data.Subhead,
				"labels":  collect_tags.Data.Labels,
			}
		}
	}

	return &result
}

// 获取热门话题
func (l *AggregationLogic) GetHotTopic(req *types.AggregationReq) *[]map[string]interface{} {
	var (
		header    map[string]string
		home_data SecondHome
	)
	result := make([]map[string]interface{}, 0)
	uid := l.ctx.Value("uid").(int64)
	name := l.ctx.Value("vos_name").(string)
	url := l.svcCtx.Config.ITEM.COMMUNITY_URL + "/community/v3/topic/index"
	body := map[string]string{
		"type":   "2",
		"status": "1",
		"ishot":  "1",
		"limit":  "3",
	}
	if uid > 0 {
		header = map[string]string{
			"vinehoo-uid":      fmt.Sprintf("%d", uid),
			"vinehoo-vos-name": name,
		}
	}
	res, err := l.svcCtx.HttpClient.Get(url, body, header)
	if err != nil {
		return &result
	}
	json.Unmarshal(res.Body(), &home_data)

	result = home_data.Data.List
	return &result
}

// 获取Banner
func (l *AggregationLogic) GetBanner(req *types.AggregationReq) *[]map[string]interface{} {
	var (
		header    map[string]string
		home_data SecondHome
	)
	result := make([]map[string]interface{}, 0)
	url := l.svcCtx.Config.ITEM.MARKETING_CONF_URL + "/marketing-conf/v3/ad/clientlist"
	body := map[string]string{
		"type":    "1",
		"channel": "2",
		"client":  fmt.Sprintf("%d", req.Client),
		// "mid":     req.Mid,
	}
	res, err := l.svcCtx.HttpClient.Get(url, body, header)
	if err != nil {
		return &result
	}
	json.Unmarshal(res.Body(), &home_data)

	if len(home_data.Data.List) > 0 {
		for _, v := range home_data.Data.List {
			if vv, ok := v["vertical_image"]; ok && vv.(string) != "" {
				result = append(result, v)
			}
		}
	}

	return &result
}

// 获取新人活动
func (l *AggregationLogic) GetNewusersActivity(req *types.AggregationReq) *map[string]interface{} {
	var (
		header   map[string]string
		activity map[string]interface{}
	)
	result := make(map[string]interface{})
	uid := l.ctx.Value("uid").(int64)
	url := l.svcCtx.Config.ITEM.USER_URL + "/user/v3/newusers/activity?bection=1"
	body := make(map[string]string)
	if uid > 0 {
		header = map[string]string{
			"vinehoo-uid": fmt.Sprintf("%d", uid),
		}
	}
	res, err := l.svcCtx.HttpClient.Get(url, body, header)
	if err != nil {
		return &result
	}
	json.Unmarshal(res.Body(), &activity)
	if v, ok := activity["data"]; ok {
		result = v.(map[string]interface{})
	}

	return &result
}
