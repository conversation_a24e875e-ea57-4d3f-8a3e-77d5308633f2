package common

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
)

// 数组去重转字符串
func ArrayToString(arr []string, str string) string {
	var result string
	repeat := make(map[string]int8)
	for _, v := range arr {
		if v != "" {
			if _, ok := repeat[v]; !ok {
				repeat[v] = 1
				if result == "" {
					result = v
				} else {
					result = fmt.Sprintf("%s%s%s", result, str, v)
				}
			}

		}
	}
	return result
}

// map转Base64字符串
func MapToBase64String(arr interface{}) string {
	Byte, _ := json.Marshal(arr)
	str := base64.StdEncoding.EncodeToString(Byte)
	return str
}

// 数组去重写入
func ArrayDeRewrite(arr *[]string, str ...string) *[]string {
	data := *arr
	info := make(map[string]int8)
	for _, v := range data {
		info[v] = 1
	}
	for _, v := range str {
		if _, ok := info[v]; !ok {
			data = append(data, v)
		}
	}
	return &data
}

// 数组去重写入
func ArrayDuplicateRemoval(data []string) []string {
	var result []string
	if len(data) == 0 {
		return data
	}
	info := make(map[string]bool)
	for _, v := range data {
		info[v] = true
	}
	for k := range info {
		result = append(result, k)
	}
	return result
}

// 字符串追加字符
func StringAppendingCharacters(str string, new_str string) string {
	if str == "" {
		str = new_str
	} else {
		str = fmt.Sprintf("%s,%s", str, new_str)
	}
	return str
}

// 验证数组中是否存在指定值
func InArrayInt(target int64, array []int64) bool {
	for _, element := range array {
		if element == target {
			return true
		}
	}
	return false
}

// 精度加法计算
func Bcadd(num1, num2 float64, length int64) float64 {
	value := num1 + num2
	str := fmt.Sprintf("%."+fmt.Sprintf("%d", length)+"f", value)
	f, _ := strconv.ParseFloat(str, 64)

	return f
}

// 精度减法计算
func Bcsub(num1, num2 float64, length int64) float64 {
	value := num1 - num2
	str := fmt.Sprintf("%."+fmt.Sprintf("%d", length)+"f", value)
	f, _ := strconv.ParseFloat(str, 64)

	return f
}

// 精准除法
func Bcdev(num1, num2 float64, length int64) float64 {
	value := num1 / num2
	str := fmt.Sprintf("%."+fmt.Sprintf("%d", length)+"f", value)
	f, _ := strconv.ParseFloat(str, 64)

	return f
}

// 精准乘法
func BcMul(num1, num2 float64, length int64) float64 {
	value := num1 * num2
	str := fmt.Sprintf("%."+fmt.Sprintf("%d", length)+"f", value)
	f, _ := strconv.ParseFloat(str, 64)

	return f
}

// 获取数组随机不重复多个字符
func Shuffle(slice []string, num int) []string {
	n := len(slice)
	for i := n - 1; i > 0; i-- {
		j := rand.Intn(i + 1)
		slice[i], slice[j] = slice[j], slice[i]
	}
	return slice[:num]
}

// 判断字符串是否存在数组
func InArrayStr(val string, arr []string) bool {
	for _, v := range arr {
		if v == val {
			return true
		}
	}

	return false
}

// 字符串去重
func UniqueStrings(s, sep string) string {
	stringsSlice := strings.Split(s, sep)
	keys := make(map[string]bool)
	list := []string{}
	for _, entry := range stringsSlice {
		if _, value := keys[entry]; !value {
			keys[entry] = true
			list = append(list, entry)
		}
	}
	return strings.Join(list, sep)
}

// 获取字符串切片最大的值
func GetMaxValueString(s []string) int64 {
	var maxValue int64

	// 遍历ids，将每个字符串转换为整数并更新最大值
	for _, v := range s {
		currentValue, err := strconv.ParseInt(v, 10, 64)
		if err == nil && currentValue > maxValue {
			maxValue = currentValue
		}
	}
	return maxValue
}
