package marketing

import (
	"engine/api/internal/logic/marketing"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
	"net/http"
)

func GetMarketingSectionHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetMarketingSectionReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := marketing.NewGetMarketingSectionLogic(r.Context(), svcCtx)
		resp, err := l.GetMarketingSection(&req)
		result.HttpResult(r, w, resp, err)
	}
}
