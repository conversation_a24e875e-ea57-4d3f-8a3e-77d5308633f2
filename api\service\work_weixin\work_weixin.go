package work_weixin

import (
	"bytes"
	"encoding/json"
	"engine/api/internal/svc"
	"engine/common/logger"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"

	"github.com/xuri/excelize/v2"
)

type WorkWeixinLogic struct {
	SvcCtx *svc.ServiceContext
}

func NewWorkWeixinLogic(svcCtx *svc.ServiceContext) *WorkWeixinLogic {
	return &WorkWeixinLogic{
		SvcCtx: svcCtx,
	}
}

// 获取企业内部应用的access_token
func (l WorkWeixinLogic) GetToken() string {
	var info AccessToken
	url_str := l.SvcCtx.Config.ITEM.WECHART_URL + "/wechat/v3/wecom/accesstoken"
	res, _ := l.SvcCtx.HttpClient.Get(url_str, map[string]string{}, map[string]string{})
	json.Unmarshal(res.Body(), &info)
	return info.AccessToken
}

func (l WorkWeixinLogic) UpTempFileToWechat(filePath, fileName string) (map[string]interface{}, error) {
	accessToken := l.GetToken()
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/media/upload?access_token=%s&type=file", accessToken)

	// 读取文件内容
	file, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	// 创建一个buffer来存储文件内容
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// 创建表单字段
	part, err := writer.CreateFormFile("media", fileName)
	if err != nil {
		return nil, err
	}

	// 将文件内容拷贝到表单字段中
	_, err = io.Copy(part, file)
	if err != nil {
		return nil, err
	}

	// 关闭multipart writer
	err = writer.Close()
	if err != nil {
		return nil, err
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", url, body)
	if err != nil {
		return nil, err
	}

	// 设置请求头
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 解析JSON响应
	var result map[string]interface{}
	err = json.Unmarshal(responseBody, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// 发送企业微信中台文件通知
func (l WorkWeixinLogic) SendFileNotice(excel_data [][]interface{}, fileName string, userid []string) {
	// 创建一个新的Excel文件实例。
	f := excelize.NewFile()

	// 设置工作表的第一个单元格的值。
	index, _ := f.NewSheet("Sheet1")

	// 写入数据行。
	rowNum := 1
	for rowIndex, row := range excel_data {
		for colIndex, cellValue := range row {
			cellName, _ := excelize.CoordinatesToCellName(colIndex+1, rowIndex+1) // 标题行固定为第一行
			f.SetCellValue("Sheet1", cellName, cellValue)
		}
		rowNum++
	}
	cwd, _ := os.Getwd()

	// 设置工作簿的默认工作表。
	f.SetActiveSheet(index)
	filePath := cwd + "/work_weixin.xlsx"
	// 保存文件。
	if err := f.SaveAs(filePath); err != nil {
		logger.E("发送企业微信中台文件通知 保存文件失败 ", err)
		return
	}
	// 删除文件
	defer os.Remove(filePath)

	res, err := l.UpTempFileToWechat(filePath, fileName)
	if err != nil {
		logger.E("发送企业微信中台文件通知 上传文件失败 ", err)
		return
	}
	if v, ok := res["media_id"]; ok {
		url_str := l.SvcCtx.Config.ITEM.WECHART_URL + "/wechat/v3/wecom/app/send"
		for _, u := range userid {
			data := map[string]interface{}{
				"userid":  u,
				"content": v,
				"msgtype": "file",
				"agentid": 0,
			}
			go l.SvcCtx.HttpClient.PostJson(url_str, data, map[string]string{})
		}

	} else {
		logger.E("发送企业微信中台文件通知 上传文件失败 ", res)
		return
	}
}
