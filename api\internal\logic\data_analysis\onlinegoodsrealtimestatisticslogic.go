package data_analysis

import (
	"context"
	"sync"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common"
	"engine/common/xerr"
	"engine/common/xtime"

	"github.com/zeromicro/go-zero/core/logx"
)

type OnlineGoodsRealtimeStatisticsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewOnlineGoodsRealtimeStatisticsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *OnlineGoodsRealtimeStatisticsLogic {
	return &OnlineGoodsRealtimeStatisticsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

var (
	goodsTable = map[string]string{
		"0":  "vh_periods_flash",
		"1":  "vh_periods_second",
		"2":  "vh_periods_cross",
		"3":  "vh_periods_leftover",
		"11": "vh_goods",
	}
	orderTable = map[string]string{
		"0":  "vh_flash_order",
		"1":  "vh_second_order",
		"2":  "vh_cross_order",
		"3":  "vh_tail_order",
		"11": "vh_orders",
	}
)

func (l *OnlineGoodsRealtimeStatisticsLogic) OnlineGoodsRealtimeStatistics(req *types.OnlineGoodsRealtimeStatisticsReq) (resp []types.OnlineGoodsRealtimeStatisticsResp, err error) {
	var (
		shangxin   sync.Map
		order_data sync.Map
		forsale    sync.Map
		wg         sync.WaitGroup
	)
	date_all := xtime.GetMonthAllDate(req.Month)

	//获取月统计开始时间戳和结束时间戳
	stime, etime := xtime.GetMonthStartAndEndTimestamp(req.Month)
	if stime == 0 || etime == 0 {
		return resp, xerr.NewParamErrMsg("无法解析年份或月份")
	}

	// 统计频道
	period_types := []string{"0", "1", "2", "3", "11"}

	for _, v := range period_types {
		wg.Add(1)
		go func(v string) {
			defer wg.Done()
			goods := make([]CommoditiesPeriods, 0)

			database := l.svcCtx.DbCommodities
			if v == "11" { //拍卖
				database = l.svcCtx.DbAuction
			}
			// 查询在售
			query := database.Table(goodsTable[v])
			switch v {
			case "0":
				query = query.Where("(sell_time <= ? AND sold_out_time >= ?) OR (sell_time >= ? AND sell_time <= ?) OR (sold_out_time >= ? AND sold_out_time <= ?)", stime, etime, stime, etime, stime, etime).Select("id", "sell_time", "sold_out_time", "is_channel")
			case "11":
				query = query.Where("(sell_time <= ? AND closing_auction_time >= ?) OR (sell_time >= ? AND sell_time <= ?) OR (closing_auction_time >= ? AND closing_auction_time <= ?)", stime, etime, stime, etime, stime, etime).Select("id", "sell_time", "closing_auction_time as sold_out_time", "issue_type")
			default:
				query = query.Where("(sell_time <= ? AND sold_out_time >= ?) OR (sell_time >= ? AND sell_time <= ?) OR (sold_out_time >= ? AND sold_out_time <= ?)", stime, etime, stime, etime, stime, etime).Select("id", "sell_time", "sold_out_time")
			}
			query.Scan(&goods)

			forsale.Store(v, goods)
		}(v)

		wg.Add(1)
		go func(v string) {
			defer wg.Done()
			var goods_ids []int64
			goods := make([]CommoditiesPeriods, 0)
			orders := make([]OrdersData, 0)
			database := l.svcCtx.DbCommodities
			if v == "11" { //拍卖
				database = l.svcCtx.DbAuction
			}
			// 查询上新
			query := database.Table(goodsTable[v])
			switch v {
			case "0":
				query = query.Where("onsale_time BETWEEN ? AND ?", stime, etime).Select("id", "onsale_time", "is_channel")
			case "11":
				query = query.Where("sell_time BETWEEN ? AND ?", stime, etime).Select("id", "sell_time as onsale_time", "issue_type")
			default:
				query = query.Where("onsale_time BETWEEN ? AND ?", stime, etime).Select("id", "onsale_time")
			}
			query.Scan(&goods)

			shangxin.Store(v, goods)
			if len(goods) > 0 {
				for _, g := range goods {
					goods_ids = append(goods_ids, g.Id)
				}
				if v == "11" { //拍卖
					// 查询订单数据
					l.svcCtx.DbAuction.Table(orderTable[v]).
						Where("created_time BETWEEN ? AND ? AND goods_id IN ? AND order_status in (1,2,3) AND payment_amount - refund_money != 0", stime, etime, goods_ids).
						Select("id", "payment_amount", "created_time", "goods_id as period").
						Scan(&orders)

				} else {
					// 查询订单数据
					l.svcCtx.DbOrders.Table(orderTable[v]).
						Where("created_time BETWEEN ? AND ? AND period IN ? AND sub_order_status in (1,2,3) AND payment_amount - refund_money != 0", stime, etime, goods_ids).
						Select("id", "payment_amount", "created_time", "period").
						Scan(&orders)
				}
			}
			order_data.Store(v, orders)
		}(v)
	}
	wg.Wait()

	for _, v := range date_all {
		var row types.OnlineGoodsRealtimeStatisticsResp

		row.Date = v
		// 获取星期几
		row.Week = xtime.GetDateweek(v)
		//当前日期开始时间和结束时间
		starttime, endtime := xtime.GetDateStartAndEnd(v)

		for _, p := range period_types {
			// 订单数据
			order, _ := order_data.Load(p)
			// 在售商品数据
			forsale, _ := forsale.Load(p)
			// 上新商品数据
			shangxin, _ := shangxin.Load(p)
			switch p {
			case "0": //闪购
				//渠道期数
				channel_id := make(map[int64]int64)
				for _, g := range shangxin.([]CommoditiesPeriods) {
					if g.OnsaleTime >= starttime && g.OnsaleTime <= endtime {
						if g.IsChannel == 1 { //闪购渠道
							channel_id[g.Id] = g.Id
							row.ChannelNews = row.ChannelNews + 1
						} else {
							row.FlashNews = row.FlashNews + 1
						}
					}
				}

				//在售
				for _, g := range forsale.([]CommoditiesPeriods) {
					if (g.SellTime <= starttime && g.SoldOutTime >= endtime) ||
						(g.SellTime > starttime && g.SellTime <= endtime) ||
						(g.SoldOutTime >= starttime && g.SoldOutTime <= endtime) {
						if g.IsChannel == 1 { //闪购渠道
							row.ChannelSale = row.ChannelSale + 1
						} else {
							row.FlashSale = row.FlashSale + 1
						}
					}
				}
				//订单数据
				for _, o := range order.([]OrdersData) {
					if o.CreatedTime >= starttime && o.CreatedTime <= endtime {
						if _, ok := channel_id[o.Period]; ok { //闪购渠道
							row.ChannelOrders = row.ChannelOrders + 1
							row.ChannelSales = common.Bcadd(row.ChannelSales, o.PaymentAmount, 2)
						} else {
							row.FlashOrders = row.FlashOrders + 1
							row.FlashSales = common.Bcadd(row.FlashSales, o.PaymentAmount, 2)
						}
					}
				}

			case "1": //秒发
				//上新
				for _, g := range shangxin.([]CommoditiesPeriods) {
					if g.OnsaleTime >= starttime && g.OnsaleTime <= endtime {
						row.SecondNews = row.SecondNews + 1
					}
				}
				//在售
				for _, g := range forsale.([]CommoditiesPeriods) {
					if (g.SellTime <= starttime && g.SoldOutTime >= endtime) ||
						(g.SellTime > starttime && g.SellTime <= endtime) ||
						(g.SoldOutTime >= starttime && g.SoldOutTime <= endtime) {
						row.SecondSale = row.SecondSale + 1
					}
				}
				//订单数据
				for _, o := range order.([]OrdersData) {
					if o.CreatedTime >= starttime && o.CreatedTime <= endtime {
						row.SecondOrders = row.SecondOrders + 1
						row.SecondSales = common.Bcadd(row.SecondSales, o.PaymentAmount, 2)
					}
				}
			case "2": //跨境
				//上新
				for _, g := range shangxin.([]CommoditiesPeriods) {
					if g.OnsaleTime >= starttime && g.OnsaleTime <= endtime {
						row.CrossNews = row.CrossNews + 1
					}
				}
				//在售
				for _, g := range forsale.([]CommoditiesPeriods) {
					if (g.SellTime <= starttime && g.SoldOutTime >= endtime) ||
						(g.SellTime > starttime && g.SellTime <= endtime) ||
						(g.SoldOutTime >= starttime && g.SoldOutTime <= endtime) {
						row.CrossSale = row.CrossSale + 1
					}
				}
				//订单数据
				for _, o := range order.([]OrdersData) {
					if o.CreatedTime >= starttime && o.CreatedTime <= endtime {
						row.CrossOrders = row.CrossOrders + 1
						row.CrossSales = common.Bcadd(row.CrossSales, o.PaymentAmount, 2)
					}
				}
			case "3": //尾货
				//上新
				for _, g := range shangxin.([]CommoditiesPeriods) {
					if g.OnsaleTime >= starttime && g.OnsaleTime <= endtime {
						row.TailNews = row.TailNews + 1
					}
				}
				//在售
				for _, g := range forsale.([]CommoditiesPeriods) {
					if (g.SellTime <= starttime && g.SoldOutTime >= endtime) ||
						(g.SellTime > starttime && g.SellTime <= endtime) ||
						(g.SoldOutTime >= starttime && g.SoldOutTime <= endtime) {
						row.TailSale = row.TailSale + 1
					}
				}
				//订单数据
				for _, o := range order.([]OrdersData) {
					if o.CreatedTime >= starttime && o.CreatedTime <= endtime {
						row.TailOrders = row.TailOrders + 1
						row.TailSales = common.Bcadd(row.TailSales, o.PaymentAmount, 2)
					}
				}
			case "11": //拍卖
				personal_id := make(map[int64]int64)
				//上新
				for _, g := range shangxin.([]CommoditiesPeriods) {
					if g.OnsaleTime >= starttime && g.OnsaleTime <= endtime {
						if g.IssueType == 1 { //个人
							personal_id[g.Id] = g.Id
							row.PersonalAuctionNews = row.PersonalAuctionNews + 1
						} else {
							row.AuctionNews = row.AuctionNews + 1
						}

					}
				}
				//在售
				for _, g := range forsale.([]CommoditiesPeriods) {
					if (g.SellTime <= starttime && g.SoldOutTime >= endtime) ||
						(g.SellTime > starttime && g.SellTime <= endtime) ||
						(g.SoldOutTime >= starttime && g.SoldOutTime <= endtime) {
						if g.IssueType == 1 { //个人
							row.PersonalAuctionSale = row.PersonalAuctionSale + 1
						} else {
							row.AuctionSale = row.AuctionSale + 1
						}
					}
				}
				//订单数据
				for _, o := range order.([]OrdersData) {
					if o.CreatedTime >= starttime && o.CreatedTime <= endtime {
						if _, ok := personal_id[o.Period]; ok { //个人拍品
							row.PersonalAuctionOrders = row.PersonalAuctionOrders + 1
							row.PersonalAuctionSales = common.Bcadd(row.PersonalAuctionSales, o.PaymentAmount, 2)
						} else {
							row.AuctionOrders = row.AuctionOrders + 1
							row.AuctionSales = common.Bcadd(row.AuctionSales, o.PaymentAmount, 2)
						}

					}
				}
			}
		}

		resp = append(resp, row)
	}

	return resp, nil
}
