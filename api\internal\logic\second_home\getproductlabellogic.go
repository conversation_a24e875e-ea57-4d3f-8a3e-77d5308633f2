package second_home

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"sync"
	"time"

	"engine/api/internal/service/fullreductionlabel"
	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/gomodule/redigo/redis"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
)

type GetProductLabelLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetProductLabelLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetProductLabelLogic {
	return &GetProductLabelLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

type ReductionRule struct {
	Id           int64
	Type         int64
	Assign       string
	Fill         float64
	Decrease     float64
	StackingNumb int64
}

type ReductionData struct {
	Id         int64
	Periods    []int64
	ActivityId []int64
	Rule       []ReductionRule
}

type SpecialActivityGoods struct {
	ActivityId int64
	Periods    int64
}

type TopLabel struct { //排序安数组顺序排列
	MonthH  []int64 `json:"month_h"`  //红葡萄酒月销量
	MonthB  []int64 `json:"month_b"`  //白葡萄酒月销量
	MonthL  []int64 `json:"month_l"`  //烈酒月销量
	MonthXq []int64 `json:"month_xq"` //起泡&香槟月销量
	WeekAll []int64 `json:"week_all"` //近一周销量

}

type PeriodData struct {
	Id int64
}

type PackageData struct {
	Id                    int64
	PeriodId              int64
	PreferentialReduction float64
}

type PeriodsClass struct {
	FlashPeriod           []int64 //闪购
	SecondPeriod          []int64 //秒发
	CrossPeriod           []int64 //跨境
	LeftoverPeriod        []int64 //尾货
	SecondMerchantsPeriod []int64 //秒发商家
}

func (l *GetProductLabelLogic) GetProductLabel(req *types.GetProductLabelReq) (resp *types.GetProductLabelResp, err error) {
	var (
		top_label      *map[int64][]types.ProductLabelTypes
		left_top_label *map[int64][]types.ProductLabelTypes
		periods        []int64
	)
	//响应
	result := types.GetProductLabelResp{
		List: make(map[int64]types.GetProductLabelRespList),
	}

	//期数标签
	period_label := make(map[int64][]types.ProductLabelTypes)

	//所有期数
	for _, v := range req.Data {
		periods = append(periods, v.Periods)
	}
	//期数分类
	periods_class := l.GetPeriodsClass(req)

	mr.Finish(func() (err error) {
		//查询top标签
		top_label = l.GetTopLabel(req)
		return
	}, func() (err error) {
		//查询每日秒杀标签
		period_data := l.GetDailyFlashKillLabel(periods)
		if len(*period_data) > 0 {
			for k, v := range *period_data {
				period_label[k] = append(period_label[k], v...)
			}
		}
		return
	}, func() (err error) {
		//查询本周上新标签
		period_data := l.GetNewThisWeekLabel(periods_class)
		if len(*period_data) > 0 {
			for k, v := range *period_data {
				period_label[k] = append(period_label[k], v...)
			}
		}
		return
	}, func() (err error) {
		//查询拼团价标签
		period_data := l.GetGroupPriceLabel(periods_class)
		if len(*period_data) > 0 {
			for k, v := range *period_data {
				period_label[k] = append(period_label[k], v...)
			}
		}
		return
	}, func() (err error) {
		if req.IsNewUser == "" || req.IsNewUser == "1" {
			//查询新人价标签
			period_data := l.GetNewcomerPriceLabel(periods_class)
			if len(*period_data) > 0 {
				for k, v := range *period_data {
					period_label[k] = append(period_label[k], v...)
				}
			}
		}
		return
	}, func() (err error) {
		//查询立减标签
		period_data := l.GetVerticalReductionLabel(periods_class)
		if len(*period_data) > 0 {
			for k, v := range *period_data {
				period_label[k] = append(period_label[k], v...)
			}
		}
		return
	}, func() (err error) {
		//查询下单返券标签
		period_data := l.GetPlaceOrderRebateLabel(periods_class)
		if len(*period_data) > 0 {
			for k, v := range *period_data {
				period_label[k] = append(period_label[k], v...)
			}
		}
		return
	}, func() (err error) {
		//查询满减标签
		period_data := fullreductionlabel.NewFullreductionlabelLogic(l.svcCtx).GetLabel(&periods, periods_class)

		if len(*period_data) > 0 {
			for k, v := range *period_data {
				period_label[k] = append(period_label[k], v...)
			}
		}
		return
	}, func() (err error) {
		//查询冷链包邮标签
		left_top_label = l.GetColdChainPackageLabel(periods_class)
		return
	})

	for _, v := range periods {
		//产品标签
		product_label := make([]types.ProductLabelTypes, 0)
		period_label_info := *LabelSorting(&period_label)
		if vv, ok := period_label_info[v]; ok {
			product_label = vv
		}
		//top标签
		top_tag := make([]types.ProductLabelTypes, 0)
		top_label_data := *LabelSorting(top_label)
		if vv, ok := top_label_data[v]; ok {
			top_tag = vv
		}
		//左上角标签
		left_top_tag := make([]types.ProductLabelTypes, 0)
		left_top_label_data := *LabelSorting(left_top_label)
		if vv, ok := left_top_label_data[v]; ok {
			left_top_tag = vv
		}
		//响应数据
		result.List[v] = types.GetProductLabelRespList{
			ProductLabel: product_label,
			TopLabel:     top_tag,
			LeftTopLabel: left_top_tag,
		}
	}

	return &result, nil
}

// 查询冷链包邮标签
func (l *GetProductLabelLogic) GetColdChainPackageLabel(periods_class *[][]int64) *map[int64][]types.ProductLabelTypes {
	var (
		teble string
		wg    sync.WaitGroup
	)
	//期数标签
	period_label := make(map[int64][]types.ProductLabelTypes)
	for k, v := range *periods_class {
		switch k {
		case 0: //闪购
			teble = "vh_periods_flash"
		case 1: //秒发
			teble = "vh_periods_second"
		case 2: //跨境
			teble = "vh_periods_cross"
		case 3: //尾货
			teble = "vh_periods_leftover"
		case 4: //秒发商家
			teble = "vh_periods_second_merchants"
		}
		if len(v) > 0 {
			wg.Add(1)
			go func(periods []int64, teble string) {
				var period_data []PeriodData
				defer wg.Done()
				l.svcCtx.DbCommodities.Table(teble).
					Where("id IN ? and is_cold_free_shipping = 1", periods).
					Select("id").
					Scan(&period_data)
				period_row := make(map[int64]int64)
				for _, v := range period_data {
					period_row[v.Id] = v.Id
				}
				for _, v := range periods {
					period_label[v] = make([]types.ProductLabelTypes, 0)
					if _, ok := period_row[v]; ok {
						period_label[v] = append(period_label[v], types.ProductLabelTypes{
							Id:    100,
							Type:  7,
							Title: "温控包邮",
						})
					}
				}
			}(v, teble)
		}
	}
	wg.Wait()
	return &period_label
}

// 查询下单返券标签
func (l *GetProductLabelLogic) GetPlaceOrderRebateLabel(periods_class *[][]int64) *map[int64][]types.ProductLabelTypes {
	var (
		teble string
		wg    sync.WaitGroup
	)
	//期数标签
	period_label := make(map[int64][]types.ProductLabelTypes)
	for k, v := range *periods_class {
		switch k {
		case 0: //闪购
			teble = "vh_periods_flash_set"
		case 1: //秒发
			teble = "vh_periods_second_set"
		case 2: //跨境
			teble = "vh_periods_cross_set"
		case 3: //尾货
			teble = "vh_periods_leftover_set"
		case 4: //秒发商家
			teble = "vh_periods_second_merchants_set"
		}
		if len(v) > 0 {
			wg.Add(1)
			go func(periods []int64, teble string) {
				var package_data []PackageData
				package_row := make(map[int64]PackageData)
				defer wg.Done()
				l.svcCtx.DbCommodities.Table(teble).
					Where("period_id IN ? and coupons_id <> 0 and is_hidden = 0", periods).
					Select("id", "period_id").
					Scan(&package_data)
				for _, v := range package_data {
					package_row[v.PeriodId] = v
				}
				for _, v := range periods {
					if _, ok := package_row[v]; ok {
						period_label[v] = append(period_label[v], types.ProductLabelTypes{
							Id:    5,
							Type:  1,
							Title: "下单返券",
						})
					}
				}
			}(v, teble)
		}
	}
	wg.Wait()
	return &period_label
}

// 查询立减标签
func (l *GetProductLabelLogic) GetVerticalReductionLabel(periods_class *[][]int64) *map[int64][]types.ProductLabelTypes {
	var (
		teble string
		wg    sync.WaitGroup
	)
	//期数标签
	period_label := make(map[int64][]types.ProductLabelTypes)
	for k, v := range *periods_class {
		switch k {
		case 0: //闪购
			teble = "vh_periods_flash_set"
		case 1: //秒发
			teble = "vh_periods_second_set"
		case 2: //跨境
			teble = "vh_periods_cross_set"
		case 3: //尾货
			teble = "vh_periods_leftover_set"
		case 4: //秒发商家
			teble = "vh_periods_second_merchants_set"
		}
		if len(v) > 0 {
			wg.Add(1)
			go func(periods []int64, teble string) {
				var package_data []PackageData
				defer wg.Done()
				l.svcCtx.DbCommodities.Table(teble).
					Where("period_id IN ? and preferential_reduction > 0 and is_hidden = 0", periods).
					Select("id", "period_id", "preferential_reduction").
					Scan(&package_data)
				package_row := make(map[int64]PackageData)
				for _, v := range package_data {
					package_row[v.PeriodId] = v
				}
				for _, v := range periods {
					if vv, ok := package_row[v]; ok {
						period_label[v] = append(period_label[v], types.ProductLabelTypes{
							Id:    3,
							Type:  2,
							Title: fmt.Sprintf("立减%g", vv.PreferentialReduction),
						})
					}
				}
			}(v, teble)
		}
	}
	wg.Wait()
	return &period_label
}

// 查询新人价标签
func (l *GetProductLabelLogic) GetNewcomerPriceLabel(periods_class *[][]int64) *map[int64][]types.ProductLabelTypes {
	var (
		teble string
		wg    sync.WaitGroup
	)
	//期数标签
	period_label := make(map[int64][]types.ProductLabelTypes)
	for k, v := range *periods_class {
		switch k {
		case 0: //闪购
			teble = "vh_periods_flash"
		case 1: //秒发
			teble = "vh_periods_second"
		case 2: //跨境
			teble = "vh_periods_cross"
		case 3: //尾货
			teble = "vh_periods_leftover"
		case 4: //秒发商家
			teble = "vh_periods_second_merchants"
		}
		if len(v) > 0 {
			wg.Add(1)
			go func(periods []int64, teble string) {
				var period_data []PeriodData
				defer wg.Done()
				l.svcCtx.DbCommodities.Table(teble).
					Where("id IN ? and marketing_attribute like '%2%'", periods).
					Select("id").
					Scan(&period_data)
				period_row := make(map[int64]int64)
				for _, v := range period_data {
					period_row[v.Id] = v.Id
				}
				for _, v := range periods {
					if _, ok := period_row[v]; ok {
						period_label[v] = append(period_label[v], types.ProductLabelTypes{
							Id:    2,
							Type:  1,
							Title: "新人价",
						})
					}
				}
			}(v, teble)
		}
	}
	wg.Wait()
	return &period_label
}

// 查询拼团价标签
func (l *GetProductLabelLogic) GetGroupPriceLabel(periods_class *[][]int64) *map[int64][]types.ProductLabelTypes {
	var (
		teble string
		wg    sync.WaitGroup
	)
	//期数标签
	period_label := make(map[int64][]types.ProductLabelTypes)
	for k, v := range *periods_class {
		switch k {
		case 0: //闪购
			teble = "vh_periods_flash"
		case 1: //秒发
			teble = "vh_periods_second"
		case 2: //跨境
			teble = "vh_periods_cross"
		case 3: //尾货
			teble = "vh_periods_leftover"
		case 4: //秒发商家
			teble = "vh_periods_second_merchants"
		}
		if len(v) > 0 {
			wg.Add(1)
			go func(periods []int64, teble string) {
				var period_data []PeriodData
				defer wg.Done()
				l.svcCtx.DbCommodities.Table(teble).
					Where("id IN ? and marketing_attribute like '%1%'", periods).
					Select("id").
					Scan(&period_data)
				period_row := make(map[int64]int64)
				for _, v := range period_data {
					period_row[v.Id] = v.Id
				}
				for _, v := range periods {
					if _, ok := period_row[v]; ok {
						period_label[v] = append(period_label[v], types.ProductLabelTypes{
							Id:    4,
							Type:  1,
							Title: "拼团价",
						})
					}
				}
			}(v, teble)
		}
	}
	wg.Wait()
	return &period_label
}

// 查询本周上新标签
func (l *GetProductLabelLogic) GetNewThisWeekLabel(periods_class *[][]int64) *map[int64][]types.ProductLabelTypes {
	var (
		teble string
		wg    sync.WaitGroup
	)
	//期数标签
	period_label := make(map[int64][]types.ProductLabelTypes)
	// 本周开始时间戳（周一）
	// startOfWeek := time.Now().AddDate(0, 0, -int(time.Now().Weekday()))
	// year, month, day := startOfWeek.Date()
	// week_start := time.Date(year, month, day, 0, 0, 0, 0, time.Local).Unix() + 86400
	// 本周结束时间戳
	week_end := int64(time.Now().Unix())
	//  时间戳转时间
	t := time.Unix(week_end-(86400*7), 0)
	//  指定日期的  0  点
	zeroTime := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
	//  时间转时间戳（本周开始时间戳）
	week_start := zeroTime.Unix()

	for k, v := range *periods_class {
		switch k {
		case 0: //闪购
			teble = "vh_periods_flash"
			continue
		case 1: //秒发
			teble = "vh_periods_second"
		case 2: //跨境
			teble = "vh_periods_cross"
			continue
		case 3: //尾货
			teble = "vh_periods_leftover"
			continue
		case 4: //秒发商家
			teble = "vh_periods_second_merchants"
		}
		if len(v) > 0 {
			wg.Add(1)
			go func(periods []int64, teble string) {
				var period_data []PeriodData
				defer wg.Done()
				l.svcCtx.DbCommodities.Table(teble).
					Where("id IN ? and onsale_time BETWEEN ? AND ?", periods, week_start, week_end).
					Select("id").
					Scan(&period_data)
				period_row := make(map[int64]int64)
				for _, v := range period_data {
					period_row[v.Id] = v.Id
				}
				for _, v := range periods {
					if _, ok := period_row[v]; ok {
						period_label[v] = append(period_label[v], types.ProductLabelTypes{
							Id:    8,
							Type:  3,
							Title: "本周上新",
						})
					}
				}
			}(v, teble)
		}
	}
	wg.Wait()
	return &period_label
}

// 查询每日秒杀标签
func (l *GetProductLabelLogic) GetDailyFlashKillLabel(periods []int64) *map[int64][]types.ProductLabelTypes {
	var period_data []PeriodData
	stime := int64(time.Now().Unix())
	//期数标签
	period_label := make(map[int64][]types.ProductLabelTypes)
	//查询每日秒杀商品
	l.svcCtx.DbMarketing.Table("vh_special_activity as a").
		Joins("left join vh_special_activity_goods as g on a.id = g.activity_id").
		Where("a.id = 37 and g.status = 2 and a.start_at <= ? and a.end_at >= ? and  g.periods IN ?", stime, stime, periods).
		Select("g.periods as id").
		Scan(&period_data)
	period_info := make(map[int64]int64)
	for _, v := range period_data {
		period_info[v.Id] = v.Id
	}
	for _, v := range periods {
		if _, ok := period_info[v]; ok {
			period_label[v] = append(period_label[v], types.ProductLabelTypes{
				Id:    1,
				Type:  1,
				Title: "每日秒杀",
			})
		}
	}
	return &period_label
}

// 查询top标签
func (l *GetProductLabelLogic) GetTopLabel(req *types.GetProductLabelReq) *map[int64][]types.ProductLabelTypes {
	var (
		top_label TopLabel
		body_str  string
		value     string
		key       string
	)

	//查询redis
	ress, _ := redis.ByteSlices(l.svcCtx.RDbSix.Invoke(func(conn redis.Conn) (interface{}, error) {
		return conn.Do("HGETALL", "period.top")
	}))

	for k, v := range ress {
		if k%2 == 0 {
			value = "[]"
			key = string(v)
			if len(ress) > k+1 {
				value = string(ress[k+1])
			}
			if body_str == "" {
				body_str = fmt.Sprintf(`"%s":%s`, key, value)
			} else {
				body_str = fmt.Sprintf(`"%s":%s,%s`, key, value, body_str)
			}
		}
	}

	if body_str != "" {
		body_str = fmt.Sprintf(`{%s}`, body_str)
	}
	json.Unmarshal([]byte(body_str), &top_label)
	period_top := make(map[int64][]types.ProductLabelTypes)
	for _, v := range req.Data {
		period_top[v.Periods] = make([]types.ProductLabelTypes, 0)
		//近一周销量
		l.MatchTopTags(v.Periods, top_label.WeekAll, "近一周销量", 30, &period_top)
		//红葡萄酒月销量
		l.MatchTopTags(v.Periods, top_label.MonthH, "红葡萄酒月销量", 50, &period_top)
		//白葡萄酒月销量
		l.MatchTopTags(v.Periods, top_label.MonthB, "白葡萄酒月销量", 51, &period_top)
		//烈酒月销量
		l.MatchTopTags(v.Periods, top_label.MonthL, "烈酒月销量", 52, &period_top)
		//起泡&香槟月销量
		l.MatchTopTags(v.Periods, top_label.MonthXq, "起泡&香槟月销量", 53, &period_top)
	}
	return &period_top
}

// 匹配top标签
func (l *GetProductLabelLogic) MatchTopTags(period int64, top_label []int64, Title string, label_id int64, period_top *map[int64][]types.ProductLabelTypes) {
	var label_type int64
	label_data := *period_top
	if len(top_label) > 0 {
		for k, v := range top_label {
			if k > 2 {
				break
			}
			if v == period {
				switch k {
				case 0:
					label_type = 4
				case 1:
					label_type = 5
				case 2:
					label_type = 6
				}
				label_data[period] = append(label_data[period], types.ProductLabelTypes{
					Id:    label_id,
					Type:  label_type,
					Title: Title,
				})
			}
		}
	}
	period_top = &label_data
}

// 获取商品分类
func (l *GetProductLabelLogic) GetPeriodsClass(req *types.GetProductLabelReq) *[][]int64 {
	var (
		periods_class PeriodsClass
		period_info   [][]int64
	)
	for _, v := range req.Data {
		switch v.PeriodsType {
		case 0: //闪购
			periods_class.FlashPeriod = append(periods_class.FlashPeriod, v.Periods)
		case 1: //秒发
			periods_class.SecondPeriod = append(periods_class.SecondPeriod, v.Periods)
		case 2: //跨境
			periods_class.CrossPeriod = append(periods_class.CrossPeriod, v.Periods)
		case 3: //尾货
			periods_class.LeftoverPeriod = append(periods_class.LeftoverPeriod, v.Periods)
		case 9: //秒发商家
			periods_class.SecondMerchantsPeriod = append(periods_class.SecondMerchantsPeriod, v.Periods)
		}
	}

	period_info = append(period_info,
		periods_class.FlashPeriod,           //闪购
		periods_class.SecondPeriod,          //秒发
		periods_class.CrossPeriod,           //跨境
		periods_class.LeftoverPeriod,        //尾货
		periods_class.SecondMerchantsPeriod, //秒发商家
	)
	return &period_info
}

// 标签排序
func LabelSorting(period_label *map[int64][]types.ProductLabelTypes) *map[int64][]types.ProductLabelTypes {
	result := make(map[int64][]types.ProductLabelTypes)

	for k, v := range *period_label {
		row := v
		sort.Slice(row, func(i, j int) bool {
			return row[i].Id < row[j].Id
		})
		result[k] = row
	}

	return &result
}
