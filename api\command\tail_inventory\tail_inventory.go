package tail_inventory

import (
	"encoding/json"
	"engine/common/config"
	"engine/common/httpClient"
	"engine/common/logger"
	"engine/common/xtime"
	"fmt"
	"strings"
	"sync"
	"time"

	"gorm.io/gorm"
)

// 全局变量
var (
	// todayTimestamp 今日0点时间戳
	todayTimestamp int64
)

// 系统常量
const (
	batchSize       = 100               // 批量处理数量
	warehouseIds    = "(4,13,314)"      // 仓库ID列表 [闪购、秒发、食品]
	inventoryLimit  = "BETWEEN 1 and 6" // 库存数量限制范围
	defaultCapacity = 64                // 默认切片容量
)

// 商品状态常量
const (
	statusOnSale1  = 1 // 在售状态1
	statusOnSale2  = 2 // 在售状态2
	statusOffSale1 = 3 // 下架状态1
	statusOffSale2 = 4 // 下架状态2
)

/*尾货库存页面（每天早上7点查询萌牙【闪购、秒发、食品】仓的库存总合小于等于6的简码，并展示出是否有在售期数，及剩余可售库存）*/
type TailInventoryLogic struct {
	config     config.ApiConfig
	db         *gorm.DB
	wmsDb      *gorm.DB
	httpClient *httpClient.HttpConfig
	// 添加缓存channel
	dataChan chan []TailInventoryWms
}

// NewTailInventoryLogic 创建新的尾货库存逻辑实例
func NewTailInventoryLogic(db, dbWms *gorm.DB, config config.ApiConfig, isHand bool) {
	l := &TailInventoryLogic{
		config:     config,
		db:         db,
		wmsDb:      dbWms,
		httpClient: httpClient.Handle(config),
		dataChan:   make(chan []TailInventoryWms, batchSize),
	}
	go l.Run(isHand)
	// 启动数据处理协程
	go l.processDataWorker()
}

// Run 运行定时任务
// isHand: 是否手动执行，为true时立即执行一次
func (l *TailInventoryLogic) Run(isHand bool) {
	// 如果是手动执行，立即运行一次
	if isHand {
		if err := l.Execute(); err != nil {
			logger.E("手动执行尾货库存处理失败:", err)
		} else {
			fmt.Println("尾货库存页--ok [手动执行]")
		}
		return
	}

	// 定时任务模式
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		now := time.Now()
		hour := now.Hour()
		minute := now.Minute()

		// 每天7点0分执行
		if hour == 7 && minute == 0 {
			if err := l.Execute(); err != nil {
				logger.E("定时执行尾货库存处理失败:", err)
			} else {
				fmt.Println("尾货库存页--ok [定时执行]")
			}
			continue
		}
		fmt.Println("尾货库存页--waiting")
	}
}

// Execute 执行处理逻辑
func (l *TailInventoryLogic) Execute() error {
	// 初始化所需的map
	fictitiousGoodsMap := make(map[string]FictitiousGoods)
	periodsIdClass := make(map[int64][]int64)
	shortCodeMap := make(map[string][]PeriodsProductInventory)
	periodsMap := make(map[int64]PeriodsInfo)

	// 设置今日时间戳
	todayTimestamp = xtime.TodayTimeStamp()

	// 查询库存数据
	var fictitiousGoods []FictitiousGoods
	if err := l.queryFictitiousGoods(&fictitiousGoods); err != nil {
		return fmt.Errorf("查询虚拟商品失败: %w", err)
	}

	// 处理库存数据
	shortCodes := l.processFictitiousGoods(fictitiousGoods, fictitiousGoodsMap)

	// 查询和处理期数产品库存
	if err := l.processPeriodsInventory(shortCodes, periodsIdClass, shortCodeMap, periodsMap); err != nil {
		return fmt.Errorf("处理期数库存失败: %w", err)
	}

	// 删除今日缓存数据
	if err := l.db.Where("created_time > ?", todayTimestamp).Delete(&TailInventoryWms{}).Error; err != nil {
		return fmt.Errorf("删除缓存数据失败: %w", err)
	}

	// 批量处理并保存数据
	if err := l.saveInventoryData(fictitiousGoodsMap, shortCodeMap, periodsMap); err != nil {
		return fmt.Errorf("保存库存数据失败: %w", err)
	}

	return nil
}

// queryFictitiousGoods 查询虚拟商品数据
func (l *TailInventoryLogic) queryFictitiousGoods(result *[]FictitiousGoods) error {
	query := `
		SELECT 
			g.goods_name,
			g.en_goods_name,
			g.short_code,
			fg.bar_code,
			fg.fictitious_id,
			f.fictitious_name,
			SUM(fg.goods_count) as goods_count
		FROM wms_fictitious_goods fg
		LEFT JOIN wms_goods g ON g.bar_code = fg.bar_code
		LEFT JOIN wms_fictitious f ON f.fictitious_id = fg.fictitious_id
		WHERE fg.fictitious_id IN ` + warehouseIds + `
		AND fg.bar_code IN (
			SELECT bar_code 
			FROM wms_fictitious_goods 
			WHERE fictitious_id IN ` + warehouseIds + `
			GROUP BY bar_code 
			HAVING SUM(goods_count) ` + inventoryLimit + `
		)
		GROUP BY fg.bar_code, fg.fictitious_id
		HAVING goods_count > 0`

	return l.wmsDb.Raw(query).Scan(result).Error
}

// processFictitiousGoods 处理虚拟商品数据
func (l *TailInventoryLogic) processFictitiousGoods(goods []FictitiousGoods, goodsMap map[string]FictitiousGoods) []string {
	shortCodes := make([]string, 0, len(goods))
	for _, v := range goods {
		shortCodes = append(shortCodes, v.ShortCode)
		inventory := FictitiousGoodsInventory{
			FictitiousId:   v.FictitiousId,
			FictitiousName: v.FictitiousName,
			GoodsCount:     v.GoodsCount,
		}

		if val, exists := goodsMap[v.ShortCode]; exists {
			val.Inventory = append(val.Inventory, inventory)
			goodsMap[v.ShortCode] = val
		} else {
			v.Inventory = []FictitiousGoodsInventory{inventory}
			goodsMap[v.ShortCode] = v
		}
	}
	return shortCodes
}

// processPeriodsInventory 处理期数产品库存
func (l *TailInventoryLogic) processPeriodsInventory(
	shortCodes []string,
	periodsIdClass map[int64][]int64,
	shortCodeMap map[string][]PeriodsProductInventory,
	periodsMap map[int64]PeriodsInfo,
) error {
	// 使用预编译语句
	query := l.db.Model(&PeriodsProductInventory{}).
		Where("is_use_comment = 0 AND periods_type IN (0,1,3) AND short_code IN ?", shortCodes).
		Order("period DESC")

	rows, err := query.Rows()
	if err != nil {
		return fmt.Errorf("查询期数产品库存失败: %w", err)
	}
	defer rows.Close()

	// 使用游标处理大量数据
	for rows.Next() {
		var inventory PeriodsProductInventory
		if err := l.db.ScanRows(rows, &inventory); err != nil {
			return fmt.Errorf("扫描期数产品库存失败: %w", err)
		}

		periodsIdClass[inventory.PeriodsType] = append(
			periodsIdClass[inventory.PeriodsType],
			inventory.Period,
		)
		shortCodeMap[inventory.ShortCode] = append(
			shortCodeMap[inventory.ShortCode],
			inventory,
		)
	}

	// 并发处理期数信息查询
	errChan := make(chan error, len(periodsIdClass))
	var wg sync.WaitGroup

	for periodsType, periods := range periodsIdClass {
		wg.Add(1)
		go func(pType int64, pIds []int64) {
			defer wg.Done()
			if err := l.queryPeriodInfo(pType, pIds, periodsMap); err != nil {
				errChan <- err
			}
		}(periodsType, periods)
	}

	// 等待所有查询完成
	wg.Wait()
	close(errChan)

	// 检查是否有错误
	for err := range errChan {
		if err != nil {
			return fmt.Errorf("查询期数信息失败: %w", err)
		}
	}

	return nil
}

// queryPeriodInfo 新增期数信息查询方法
func (l *TailInventoryLogic) queryPeriodInfo(
	periodsType int64,
	periods []int64,
	periodsMap map[int64]PeriodsInfo,
) error {
	var periodsInfo []PeriodsInfo
	periodTable := config.PeriodTable[periodsType]

	if err := l.db.Table(periodTable).
		Where("id IN ?", periods).
		Select("id, onsale_status, sell_time, supplier, buyer_name").
		Scan(&periodsInfo).Error; err != nil {
		return err
	}

	// 使用互斥锁保护map写入
	var mu sync.Mutex
	mu.Lock()
	for _, p := range periodsInfo {
		periodsMap[p.Id] = p
	}
	mu.Unlock()

	return nil
}

// saveInventoryData 保存库存数据
func (l *TailInventoryLogic) saveInventoryData(
	fictitiousGoodsMap map[string]FictitiousGoods,
	shortCodeMap map[string][]PeriodsProductInventory,
	periodsMap map[int64]PeriodsInfo,
) error {
	timestamp := time.Now().Unix()
	data := make([]TailInventoryWms, 0, batchSize)

	for _, v := range fictitiousGoodsMap {
		inventoryRecord, err := l.buildInventoryRecord(v, shortCodeMap, periodsMap, timestamp)
		if err != nil {
			logger.E("构建库存记录失败:", err)
			continue
		}
		data = append(data, inventoryRecord)

		if len(data) >= batchSize {
			// 使用channel异步处理数据
			l.dataChan <- data
			data = make([]TailInventoryWms, 0, batchSize)
		}
	}

	if len(data) > 0 {
		l.dataChan <- data
	}

	return nil
}

// buildInventoryRecord 构建单条库存记录
// 参数:
// - v: 虚拟商品信息
// - shortCodeMap: 简码对应的期数产品库存映射
// - periodsMap: 期数信息映射
// - timestamp: 当前时间戳
// 返回:
// - TailInventoryWms: 构建的库存记录
// - error: 错误信息
func (l *TailInventoryLogic) buildInventoryRecord(
	v FictitiousGoods,
	shortCodeMap map[string][]PeriodsProductInventory,
	periodsMap map[int64]PeriodsInfo,
	timestamp int64,
) (TailInventoryWms, error) {
	var (
		isSale        int64
		periodSlice   []string
		periodInfoStr string
	)
	remainingSaleInventory := make([]RemainingSaleInventory, 0)

	// 处理库存信息
	inventoryBytes, err := json.Marshal(v.Inventory)
	if err != nil {
		return TailInventoryWms{}, fmt.Errorf("序列化库存信息失败: %w", err)
	}

	// 处理期数信息
	periodData, remainingSale := l.processPeriodsData(v.ShortCode, shortCodeMap, periodsMap)
	if len(remainingSale) > 0 {
		isSale = 1
		for _, p := range remainingSale {
			periodSlice = append(periodSlice, fmt.Sprintf("%d", p.Period))
		}
		remainingSaleInventory = remainingSale
		periodInfoStr = "{}"
	} else if len(periodData) > 0 {
		periodDataBytes, err := json.Marshal(periodData)
		if err != nil {
			return TailInventoryWms{}, fmt.Errorf("序列化期数数据失败: %w", err)
		}
		periodInfoStr = string(periodDataBytes)
	} else {
		periodInfoStr = "{}"
	}

	// 处理剩余可售库存
	remainingSaleBytes, err := json.Marshal(remainingSaleInventory)
	if err != nil {
		return TailInventoryWms{}, fmt.Errorf("序列化剩余可售库存失败: %w", err)
	}

	// 构建并返回库存记录
	return TailInventoryWms{
		CnGoodsName:            v.GoodsName,
		EnGoodsName:            v.EnGoodsName,
		ShortCode:              v.ShortCode,
		BarCode:                v.BarCode,
		Inventory:              string(inventoryBytes),
		IsSale:                 isSale,
		Period:                 strings.Join(periodSlice, ","),
		PeriodInfo:             periodInfoStr,
		RemainingSaleInventory: string(remainingSaleBytes),
		CreatedTime:            timestamp,
	}, nil
}

// saveInventoryBatch 批量保存库存数据
func (l *TailInventoryLogic) saveInventoryBatch(data []TailInventoryWms) error {
	// 使用事务处理批量插入
	return l.db.Transaction(func(tx *gorm.DB) error {
		return tx.Model(&TailInventoryWms{}).Create(&data).Error
	})
}

// processPeriodsData 处理期数相关数据
// 参数:
// - shortCode: 商品简码
// - shortCodeMap: 简码对应的期数产品库存映射
// - periodsMap: 期数信息映射
// 返回:
// - PeriodData: 期数数据
// - []RemainingSaleInventory: 剩余可售库存列表
func (l *TailInventoryLogic) processPeriodsData(
	shortCode string,
	shortCodeMap map[string][]PeriodsProductInventory,
	periodsMap map[int64]PeriodsInfo,
) ([]PeriodData, []RemainingSaleInventory) {
	periodData := make([]PeriodData, 0)

	remainingSaleInventory := make([]RemainingSaleInventory, 0)

	// 获取简码对应的库存信息
	inventories, exists := shortCodeMap[shortCode]
	if !exists {
		return periodData, remainingSaleInventory
	}

	// 遍历处理每个库存记录
	for _, inv := range inventories {
		periodsInfo, exists := periodsMap[inv.Period]
		if !exists {
			continue
		}

		// 处理在售期数 (状态1或2表示在售)
		if periodsInfo.OnsaleStatus == statusOnSale1 || periodsInfo.OnsaleStatus == statusOnSale2 {
			remainingSaleInventory = append(remainingSaleInventory, RemainingSaleInventory{
				Period:    inv.Period,
				Inventory: inv.Inventory,
			})
		}

		// 处理下架期数 (状态3或4表示下架)，只记录第一个下架期数的信息
		if (periodsInfo.OnsaleStatus == statusOffSale1 || periodsInfo.OnsaleStatus == statusOffSale2) && len(periodData) == 0 {
			// 查询套餐信息
			var packageInfo []PackageInfo
			productID := fmt.Sprintf("%d", inv.ProductId)
			packageTable := config.PackageTable[inv.PeriodsType]

			l.db.Table(packageTable).
				Where("is_hidden = 0 AND period_id = ? AND associated_products LIKE ?",
					inv.Period,
					"%"+productID+"%").
				Select("package_name, price").
				Scan(&packageInfo)

			periodData = append(periodData, PeriodData{
				Id:        inv.Period,
				SellTime:  xtime.Date(periodsInfo.SellTime),
				Supplier:  periodsInfo.Supplier,
				BuyerName: periodsInfo.BuyerName,
				Costprice: inv.Costprice,
				Package:   packageInfo,
			})
		}
	}

	return periodData, remainingSaleInventory
}

// 添加数据处理worker
func (l *TailInventoryLogic) processDataWorker() {
	for data := range l.dataChan {
		if err := l.saveInventoryBatch(data); err != nil {
			logger.E("保存库存数据失败:", err)
		}
	}
}
