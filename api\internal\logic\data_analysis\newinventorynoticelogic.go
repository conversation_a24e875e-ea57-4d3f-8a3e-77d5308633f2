package data_analysis

import (
	"context"

	"engine/api/command/inventory_notice"
	"engine/api/command/period_movesales_notice"
	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type NewInventoryNoticeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewNewInventoryNoticeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NewInventoryNoticeLogic {
	return &NewInventoryNoticeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *NewInventoryNoticeLogic) NewInventoryNotice(req *types.NewInventoryNoticeReq) error {

	switch req.Type {
	case 0:
		// 导出【闪购、秒发、食品】仓有库存简码，以表格形式每天早上7点推送到企微中台【杨文科、龙飞、陈泓州】
		go inventory_notice.NewInventoryNoticeLogic(l.svcCtx).Handle()
	case 1:
		// 导出【全频道在售库存动销】，以表格形式每天早上7点推送到企微中台
		go period_movesales_notice.NewPeriodMovesalesNoticeLogic(l.svcCtx).Handle()
	}

	return nil
}
