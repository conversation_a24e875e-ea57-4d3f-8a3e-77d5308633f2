package MarketingAutoAdd

import (
	"encoding/json"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common"
	"engine/common/config"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/zeromicro/go-zero/core/mr"
)

type MarketingAutoAddLogic struct {
	svcCtx *svc.ServiceContext
}

func NewMarketingAutoAddLogic(svcCtx *svc.ServiceContext) *MarketingAutoAddLogic {
	return &MarketingAutoAddLogic{
		svcCtx: svcCtx,
	}
}

var ColumnChannel = map[int64][]int64{
	0: {0, 1, 20},
	1: {0, 1, 2, 20},
	2: {0, 1, 8},
	3: {0, 1, 9, 20},
}

var CardChannel = map[int64][]int64{
	0: {0, 1},
	1: {0, 2},
	2: {0, 1, 8},
	3: {0, 1, 9},
}

// 营销版块添加期数
func (l *MarketingAutoAddLogic) MarketingSectionAddPeriod(req *types.AutomaticallyAddPeriodReq, is_appoint int64) {
	var marketing_section MarketingSection

	if is_appoint == 1 { //指定版块
		marketing_section = l.GetAppointMarketingSection(req)
	} else { //所有启用版块
		marketing_section = l.GetMarketingSection(req.Period, req.PeriodType, req.Inventory)
	}

	// 当前时间戳
	timeval := int64(time.Now().Unix())
	// 期数信息
	period_info := marketing_section.PeriodPnfo
	// 图片逗号分隔取第一张
	product_img := strings.Split(period_info.ProductImg, ",")
	if len(product_img) > 0 {
		period_info.ProductImg = product_img[0]
	}

	mr.Finish(func() (err error) { // 添加卡片商品
		if len(marketing_section.Card) == 0 {
			return nil
		}

		for _, v := range marketing_section.Card {
			var s_filter []CardGoodsFilter
			if req.Inventory == 0 && !common.InArrayInt(v.Id, req.Card) {
				continue
			}
			data := CardGoodsLive{
				Cid:        v.Id,
				CreatedAt:  timeval,
				RelationId: req.Period,
				Channel:    req.PeriodType + 1,
				Title:      req.Title,
				Type:       1,
				Image:      period_info.ProductImg,
			}
			l.svcCtx.DbMarketing.Create(&data)

			// 筛选项
			if data.Id > 0 && len(req.CardFilter) > 0 {
				for _, f := range v.Filter {
					if common.InArrayInt(f.Id, req.CardFilter) {
						s_filter = append(s_filter, CardGoodsFilter{
							GoodsId:   data.Id,
							CardId:    v.Id,
							FilterId:  f.Id,
							CreatedAt: timeval,
						})
					}
				}
				if len(s_filter) > 0 {
					l.svcCtx.DbMarketing.Create(&s_filter)
				}
			}
		}
		return

	}, func() (err error) { // 添加栏目商品
		if len(marketing_section.Column) == 0 {
			return nil
		}
		for _, v := range marketing_section.Column {
			var s_filter []ColumnGoodsFilter
			if req.Inventory == 0 && !common.InArrayInt(v.Id, req.Column) {
				continue
			}
			data := ColumnGoods{
				Cid:         v.Id,
				CreatedAt:   timeval,
				Period:      req.Period,
				PeriodsType: req.PeriodType,
				AddType:     1,
				Title:       req.Title,
				ShortName:   req.Title,
				Image:       period_info.ProductImg,
			}
			l.svcCtx.DbMarketing.Create(&data)

			// 筛选项
			if data.Id > 0 {
				for _, f := range v.Filter {
					s_filter_info := ColumnGoodsFilter{
						GoodsId:   data.Id,
						CardId:    v.Id,
						FilterId:  f.Id,
						CreatedAt: timeval,
					}

					if len(req.ColumnFilter) > 0 && common.InArrayInt(f.Id, req.ColumnFilter) {
						s_filter = append(s_filter, s_filter_info)
					} else if len(req.ColumnFilter) == 0 {
						s_filter = append(s_filter, s_filter_info)
					}
				}
				if len(s_filter) > 0 {
					l.svcCtx.DbMarketing.Create(&s_filter)
				}
			}
		}
		return
	}, func() (err error) { // 添加商品标签
		if len(marketing_section.Label) == 0 {
			return nil
		}

		for _, v := range marketing_section.Label {
			if len(req.Label) > 0 && !common.InArrayInt(v.Id, req.Label) {
				continue
			}

			if period_info.Label == "" {
				period_info.Label = fmt.Sprintf("%v", v.Id)
			} else {
				period_info.Label = fmt.Sprintf("%v,%v", period_info.Label, v.Id)
			}
		}
		// 商品表
		period_table := config.PeriodTable[req.PeriodType]
		l.svcCtx.DbCommodities.Table(period_table).Where("id = ?", req.Period).Update("label", period_info.Label)
		return
	})

}

// 获取自动添加的指定营销版块
func (l *MarketingAutoAddLogic) GetAppointMarketingSection(req *types.AutomaticallyAddPeriodReq) MarketingSection {
	// 默认返回值
	result := MarketingSection{
		Card:   make([]Card, 0),
		Column: make([]Column, 0),
		Label:  make([]RecommendLabel, 0),
	}

	// 获取期数信息和产品信息
	period_info, products := l.GetPeriodInfoAndProducts(req.Period, req.PeriodType)
	if period_info.Id == 0 || period_info.OnsaleStatus != 2 {
		return result
	}
	// 获取国家、产区、酒庄、类型所有上级
	period_info = *l.GetCountryProducingAreaChateauProductType(period_info, products)
	result.PeriodPnfo = period_info

	mr.Finish(func() (err error) { // 卡片
		var (
			channel    []int64
			info       []Card
			card_goods []CardGoodsLive
			not_cid    []int64
		)
		if len(req.Card) == 0 {
			return
		}
		// 查询期数已存在的卡片ID
		l.svcCtx.DbMarketing.Model(&card_goods).
			Where("relation_id = ? and type = 1 and cid IN ?", req.Period, req.Card).
			Scan(&card_goods)
		for _, v := range card_goods {
			not_cid = append(not_cid, v.Cid)
		}

		// 商品频道对应频道
		channel = []int64{1}
		if v, ok := CardChannel[req.PeriodType]; ok {
			channel = v
		}

		// 营销卡片
		query := l.svcCtx.DbMarketing.Model(&info).
			Where("id IN ? and add_method = 1 and channel IN ?", req.Card, channel)
		if len(not_cid) > 0 {
			query = query.Where("id not IN ?", not_cid)
		}
		query.Scan(&info)

		if len(info) == 0 {
			return
		}
		for _, v := range info {
			var auto_add_content []AutoAddContent
			json.Unmarshal([]byte(v.AutoAddContent), &auto_add_content)
			//验证定义内容
			is_meet := l.VerifyContent(v.AutoAddType, auto_add_content, period_info)
			if is_meet {
				result.Card = append(result.Card, v)
			}
		}
		return
	}, func() (err error) { // 栏目
		var (
			channel     []int64
			c_goods     []ColumnGoods
			not_cid     []int64
			info        []Column
			filter_info []ColumnFilter
		)
		if len(req.Column) == 0 {
			return
		}

		// 商品频道对应栏目频道
		channel = []int64{1}
		if v, ok := ColumnChannel[req.PeriodType]; ok {
			channel = v
		}

		// 查询期数已存在的栏目
		l.svcCtx.DbMarketing.Model(&c_goods).
			Where("period = ? and cid IN ?", req.Period, req.Column).
			Scan(&c_goods)
		for _, v := range c_goods {
			not_cid = append(not_cid, v.Cid)
		}
		// 营销栏目
		query := l.svcCtx.DbMarketing.Model(&info).Where("id IN ? and (add_method = 1 or page_mode = 1) and channel IN ?", req.Column, channel)
		if len(not_cid) > 0 {
			query = query.Where("id not IN ?", not_cid)
		}
		query.Scan(&info)

		// 筛选子项
		l.svcCtx.DbMarketing.Model(&filter_info).
			Where("is_delete = 0 and add_method = 1 and card_id IN ?", req.Column).
			Order("sort asc").
			Scan(&filter_info)
		//验证子项
		filter_map := make(map[int64][]ColumnFilter)
		for _, f := range filter_info {
			var auto_add_content []AutoAddContent
			json.Unmarshal([]byte(f.AutoAddContent), &auto_add_content)
			//验证定义内容
			is_meet := l.VerifyContent(f.AutoAddType, auto_add_content, period_info)
			if is_meet {
				filter_map[f.CardId] = append(filter_map[f.CardId], f)
			}
		}

		for _, v := range info {
			var is_meet bool
			if v.PageMode == 1 { //筛选
				if _, ok := filter_map[v.Id]; ok {
					is_meet = true
					v.Filter = filter_map[v.Id]
				}
			} else { //浏览
				var auto_add_content []AutoAddContent
				json.Unmarshal([]byte(v.AutoAddContent), &auto_add_content)
				//验证定义内容
				is_meet = l.VerifyContent(v.AutoAddType, auto_add_content, period_info)
			}

			if is_meet {
				result.Column = append(result.Column, v)
			}
		}
		return
	}, func() (err error) { // 标签
		var (
			label_id []string
			info     []RecommendLabel
		)
		if len(req.Label) == 0 {
			return
		}

		// 期数已存在的标签
		if period_info.Label != "" {
			label_id = strings.Split(period_info.Label, ",")
		}
		// 查询标签
		query := l.svcCtx.DbCommodities.Model(&info).Where("id IN ? and type = 2 and is_delete = 0 and add_method = 1", req.Label)
		if len(label_id) > 0 {
			query = query.Where("id not IN ?", label_id)
		}
		query.Scan(&info)
		for _, v := range info {
			is_meet_condition := true
			auto_add_content := make(map[string][]AutoAddContent)
			json.Unmarshal([]byte(v.AutoAddContent), &auto_add_content)
			auto_add_type := strings.Split(v.AutoAddType, ",")
			if len(auto_add_type) == 0 {
				continue
			}
			for _, t := range auto_add_type {
				var auto_content []AutoAddContent
				// 字符串转int64
				t_id, _ := strconv.ParseInt(t, 10, 64)
				if val, ok := auto_add_content[t]; ok {
					auto_content = val
				}
				//验证定义内容
				is_meet := l.VerifyContent(t_id, auto_content, period_info)
				if !is_meet {
					is_meet_condition = false
					break
				}
			}

			if is_meet_condition {
				result.Label = append(result.Label, v)
			}
		}

		return
	})

	return result
}

// 获取自动添加的营销版块
func (l *MarketingAutoAddLogic) GetMarketingSection(period, period_type, inventory int64) MarketingSection {
	// 默认返回值
	result := MarketingSection{
		Card:   make([]Card, 0),
		Column: make([]Column, 0),
		Label:  make([]RecommendLabel, 0),
	}

	// 获取期数信息和产品信息
	period_info, products := l.GetPeriodInfoAndProducts(period, period_type)
	if period_info.Id == 0 || (inventory == 1 && period_info.OnsaleStatus != 2) {
		return result
	}
	// 是否操作库存：0-否，1-是
	period_info.IsInventory = inventory
	// 获取国家、产区、酒庄、类型所有上级
	period_info = *l.GetCountryProducingAreaChateauProductType(period_info, products)
	result.PeriodPnfo = period_info

	mr.Finish(func() (err error) { // 卡片
		var (
			channel     []int64
			info        []Card
			card_goods  []CardGoodsLive
			not_cid     []int64
			ids         []int64
			filter_info []CardFilter
		)
		// 查询期数已存在的卡片ID
		l.svcCtx.DbMarketing.Model(&card_goods).
			Where("relation_id = ? and type = 1", period).
			Scan(&card_goods)
		for _, v := range card_goods {
			not_cid = append(not_cid, v.Cid)
		}
		// 商品频道对应频道
		channel = []int64{1}
		if v, ok := CardChannel[period_type]; ok {
			channel = v
		}

		// 营销卡片
		query := l.svcCtx.DbMarketing.Model(&info).Where("status = 1 and add_method = 1 and channel IN ?", channel)
		if len(not_cid) > 0 {
			query = query.Where("id not IN ?", not_cid)
		}
		query.Scan(&info)
		if len(info) == 0 {
			return
		}
		for _, v := range info {
			ids = append(ids, v.Id)
		}
		l.svcCtx.DbMarketing.Model(&filter_info).
			Where("is_delete = 0 and card_id IN ?", ids).
			Order("sort asc").
			Scan(&filter_info)

		for _, v := range info {
			var auto_add_content []AutoAddContent
			json.Unmarshal([]byte(v.AutoAddContent), &auto_add_content)
			//验证定义内容
			is_meet := l.VerifyContent(v.AutoAddType, auto_add_content, period_info)
			if is_meet {
				//筛选项
				for _, f := range filter_info {
					if f.CardId == v.Id {
						v.Filter = append(v.Filter, f)
					}
				}

				result.Card = append(result.Card, v)
			}
		}
		return
	}, func() (err error) { // 栏目
		var (
			channel     []int64
			c_goods     []ColumnGoods
			not_cid     []int64
			info        []Column
			ids         []int64
			filter_info []ColumnFilter
		)

		// 商品频道对应栏目频道
		channel = []int64{1}
		if v, ok := ColumnChannel[period_type]; ok {
			channel = v
		}

		// 查询期数已存在的栏目
		l.svcCtx.DbMarketing.Model(&c_goods).
			Where("period = ? ", period).
			Scan(&c_goods)
		for _, v := range c_goods {
			not_cid = append(not_cid, v.Cid)
		}
		// 营销栏目
		query := l.svcCtx.DbMarketing.Model(&info).Where("status = 1 and (add_method = 1 or page_mode = 1) and channel IN ?", channel)
		if len(not_cid) > 0 {
			query = query.Where("id not IN ?", not_cid)
		}
		query.Scan(&info)
		if len(info) == 0 {
			return
		}

		for _, v := range info {
			ids = append(ids, v.Id)
		}
		l.svcCtx.DbMarketing.Model(&filter_info).
			Where("is_delete = 0 and add_method = 1 and card_id IN ?", ids).
			Order("sort asc").
			Scan(&filter_info)

		//验证子项
		filter_map := make(map[int64][]ColumnFilter)
		for _, f := range filter_info {
			var auto_add_content []AutoAddContent
			json.Unmarshal([]byte(f.AutoAddContent), &auto_add_content)
			//验证定义内容
			is_meet := l.VerifyContent(f.AutoAddType, auto_add_content, period_info)
			if is_meet {
				filter_map[f.CardId] = append(filter_map[f.CardId], f)
			}
		}

		for _, v := range info {
			var is_meet bool
			if v.PageMode == 1 { //筛选
				if _, ok := filter_map[v.Id]; ok {
					is_meet = true
					v.Filter = filter_map[v.Id]
				}
			} else { //浏览
				var auto_add_content []AutoAddContent
				json.Unmarshal([]byte(v.AutoAddContent), &auto_add_content)
				//验证定义内容
				is_meet = l.VerifyContent(v.AutoAddType, auto_add_content, period_info)
			}
			if is_meet {
				result.Column = append(result.Column, v)
			}
		}
		return
	}, func() (err error) { // 标签
		var (
			label_id []string
			info     []RecommendLabel
		)
		// 期数已存在的标签
		if period_info.Label != "" {
			label_id = strings.Split(period_info.Label, ",")
		}
		// 查询标签
		query := l.svcCtx.DbCommodities.Model(&info).Where("type = 2 and is_delete = 0 and add_method = 1")
		if len(label_id) > 0 {
			query = query.Where("id not IN ?", label_id)
		}
		query.Scan(&info)
		for _, v := range info {
			is_meet_condition := true
			auto_add_content := make(map[string][]AutoAddContent)
			json.Unmarshal([]byte(v.AutoAddContent), &auto_add_content)
			auto_add_type := strings.Split(v.AutoAddType, ",")
			if len(auto_add_type) == 0 {
				continue
			}
			for _, t := range auto_add_type {
				var auto_content []AutoAddContent
				// 字符串转int64
				t_id, _ := strconv.ParseInt(t, 10, 64)
				if val, ok := auto_add_content[t]; ok {
					auto_content = val
				}
				//验证定义内容
				is_meet := l.VerifyContent(t_id, auto_content, period_info)
				if !is_meet {
					is_meet_condition = false
					break
				}
			}

			if is_meet_condition {
				result.Label = append(result.Label, v)
			}

		}

		return
	})

	return result
}

// 获取国家、产区、酒庄、类型所有上级
func (l *MarketingAutoAddLogic) GetCountryProducingAreaChateauProductType(period_info PeriodsInfo, products []Products) *PeriodsInfo {
	var (
		country_id        []int64
		chateau_id        []int64
		producing_area_id []int64
		product_type      []int64
		producing_area    []RegionsBase
		chateau           []WineryBase
		product_type_data []ProductType
		grape_id          []int64
	)
	for _, v := range products {
		// 葡萄品种
		grape := strings.Split(v.Grape, ",")
		for _, vv := range grape {
			s_grape_id, _ := strconv.ParseInt(vv, 10, 64)
			if !common.InArrayInt(s_grape_id, grape_id) {
				grape_id = append(grape_id, s_grape_id)
			}
		}
		// 国家ID
		if !common.InArrayInt(v.CountryId, country_id) {
			country_id = append(country_id, v.CountryId)
		}
		// 产区ID
		if v.ProducingAreaId != "" {
			p_area_id, _ := strconv.ParseInt(v.ProducingAreaId, 10, 64)
			if !common.InArrayInt(p_area_id, producing_area_id) {
				producing_area_id = append(producing_area_id, p_area_id)
			}
		}
		// 酒庄ID
		if v.ChateauId != "" {
			p_chateau_id, _ := strconv.ParseInt(v.ChateauId, 10, 64)
			if !common.InArrayInt(p_chateau_id, chateau_id) {
				chateau_id = append(chateau_id, p_chateau_id)
			}
		}
		// 产品类型
		if !common.InArrayInt(v.ProductType, product_type) {
			product_type = append(product_type, v.ProductType)
		}
	}
	mr.Finish(func() (err error) {
		// 查询递归查询所有上级产区
		var wg sync.WaitGroup
		for _, v := range producing_area_id {
			wg.Add(1)
			go func(v int64) {
				defer wg.Done()
				s_producing_area := l.GetProducingArea(v)
				producing_area = append(producing_area, s_producing_area...)
			}(v)
		}
		wg.Wait()
		return
	}, func() (err error) {
		// 查询递归查询所有上级酒庄
		var wg sync.WaitGroup
		for _, v := range chateau_id {
			wg.Add(1)
			go func(v int64) {
				defer wg.Done()
				s_chateau := l.GetChateau(v)
				chateau = append(chateau, s_chateau...)
			}(v)
		}
		wg.Wait()
		return
	}, func() (err error) {
		// 查询递归查询所有上级类型
		var wg sync.WaitGroup
		for _, v := range product_type {
			wg.Add(1)
			go func(v int64) {
				defer wg.Done()
				s_product_type := l.GetProductType(v)
				product_type_data = append(product_type_data, s_product_type...)
			}(v)
		}
		wg.Wait()
		return
	})
	//葡萄品种
	period_info.GrapeId = grape_id
	// 国家
	period_info.CountryId = country_id
	// 酒庄ID
	for _, v := range chateau {
		if !common.InArrayInt(v.Id, period_info.ChateauId) {
			period_info.ChateauId = append(period_info.ChateauId, v.Id)
		}
	}
	// 产区ID
	for _, v := range producing_area {
		if !common.InArrayInt(v.Id, period_info.ProducingAreaId) {
			period_info.ProducingAreaId = append(period_info.ProducingAreaId, v.Id)
		}
	}
	// 类型ID
	for _, v := range product_type_data {
		if !common.InArrayInt(v.Id, period_info.ProductType) {
			period_info.ProductType = append(period_info.ProductType, v.Id)
		}
	}
	return &period_info
}

// 获取期数信息和产品信息
func (l *MarketingAutoAddLogic) GetPeriodInfoAndProducts(period, period_type int64) (PeriodsInfo, []Products) {
	var (
		period_info   PeriodsInfo
		package_data  []PackageData
		products      []Products
		inventory_num int64
	)
	if !common.InArrayInt(period_type, []int64{0, 1, 2, 3}) {
		return period_info, products
	}
	// 商品表
	period_table := config.PeriodTable[period_type]
	// 套餐表
	package_table := config.PackageTable[period_type]

	mr.Finish(func() (err error) {
		var info Periods
		// 期数信息
		l.svcCtx.DbCommodities.Table(period_table).
			Where("id = ? and is_channel = 0", period).
			Select("id", "title", "label", "product_img", "onsale_status").
			Find(&info)
		period_info = PeriodsInfo{
			Id:           info.Id,
			Title:        info.Title,
			Label:        info.Label,
			ProductImg:   info.ProductImg,
			OnsaleStatus: info.OnsaleStatus,
		}

		return
	}, func() (err error) {
		var (
			product_id          []int64
			associated_products []AssociatedProducts
		)
		// 套餐信息
		l.svcCtx.DbCommodities.Table(package_table).
			Where("period_id = ? and is_hidden = 0", period).
			Select("id", "associated_products").
			Scan(&package_data)
		for _, v := range package_data {
			json.Unmarshal([]byte(v.AssociatedProducts), &associated_products)
			for _, vv := range associated_products {
				// ProductId有可能为数组或数值
				if reflect.TypeOf(vv.ProductId).Kind() == reflect.Slice {
					for _, vvv := range vv.ProductId.([]interface{}) {
						s_product_id_str := fmt.Sprintf("%v", vvv)
						s_product_id, _ := strconv.ParseInt(s_product_id_str, 10, 64)
						if !common.InArrayInt(s_product_id, product_id) {
							product_id = append(product_id, s_product_id)
						}
					}
				} else {
					s_product_id_str := fmt.Sprintf("%v", vv.ProductId)
					s_product_id, _ := strconv.ParseInt(s_product_id_str, 10, 64)
					if !common.InArrayInt(s_product_id, product_id) {
						product_id = append(product_id, s_product_id)
					}
				}
			}
		}
		if len(product_id) == 0 {
			return
		}
		mr.Finish(func() (err error) {
			// 查询产品信息
			l.svcCtx.DbWiki.Model(&products).Where("id IN ?", product_id).Scan(&products)
			return
		}, func() (err error) {
			var info PeriodsProductInventory
			l.svcCtx.DbCommodities.Model(&info).
				Where("period = ? and product_id IN ?", period, product_id).
				Group("period").
				Select("id", "max(inventory) as inventory").
				Take(&info)
			inventory_num = info.Inventory
			return
		})
		return
	})
	// 剩余最大库存
	period_info.Inventory = inventory_num
	return period_info, products
}

// 验证定义内容
func (l *MarketingAutoAddLogic) VerifyContent(autoadd_type int64, auto_add_content []AutoAddContent, period_info PeriodsInfo) bool {
	//操作库存非库存类型直接false
	if period_info.IsInventory == 1 && autoadd_type != 8 {
		return false
	}

	switch autoadd_type {
	case 1: //国家
		for _, vv := range auto_add_content {
			id_str := fmt.Sprintf("%v", vv.Id)
			id, _ := strconv.ParseInt(id_str, 10, 64)
			if common.InArrayInt(id, period_info.CountryId) {
				return true
			}
		}
	case 2: //产区
		for _, vv := range auto_add_content {
			id_str := fmt.Sprintf("%v", vv.Id)
			id, _ := strconv.ParseInt(id_str, 10, 64)
			if common.InArrayInt(id, period_info.ProducingAreaId) {
				return true
			}
		}
	case 3: //酒庄
		for _, vv := range auto_add_content {
			id_str := fmt.Sprintf("%v", vv.Id)
			id, _ := strconv.ParseInt(id_str, 10, 64)
			if common.InArrayInt(id, period_info.ChateauId) {
				return true
			}
		}
	case 4: //标签
		// 逗号分隔转数组
		label_id := strings.Split(period_info.Label, ",")
		for _, vv := range auto_add_content {
			if common.InArrayStr(fmt.Sprintf("%v", vv.Id), label_id) {
				return true
			}
		}
	case 5: //类型
		for _, vv := range auto_add_content {
			id_str := fmt.Sprintf("%v", vv.Id)
			id, _ := strconv.ParseInt(id_str, 10, 64)
			if common.InArrayInt(id, period_info.ProductType) {
				return true
			}
		}
	case 6: //标题
		for _, vv := range auto_add_content {
			name := fmt.Sprintf("%v", vv.Name)
			// 商品标题是否包含
			if strings.Contains(period_info.Title, name) {
				return true
			}
		}
	case 7: // 葡萄品种
		for _, vv := range auto_add_content {
			id_str := fmt.Sprintf("%v", vv.Id)
			id, _ := strconv.ParseInt(id_str, 10, 64)
			if common.InArrayInt(id, period_info.GrapeId) {
				return true
			}
		}
	case 8: //库存
		for _, vv := range auto_add_content {
			name := fmt.Sprintf("%v", vv.Name)
			// 字符串转int64
			i, _ := strconv.ParseInt(name, 10, 64)
			if period_info.Inventory > 0 && period_info.Inventory <= i {
				return true
			}
		}
	}
	return false

}

// 递归查询所有上级产区
func (l *MarketingAutoAddLogic) GetProducingArea(id int64) []RegionsBase {
	var (
		producing_area RegionsBase
		result         []RegionsBase
	)
	l.svcCtx.DbWiki.Model(&producing_area).Where("id = ?", id).Take(&producing_area)
	if producing_area.Id == 0 {
		return result
	}
	result = append(result, producing_area)
	if producing_area.ParentId != 0 {
		producing_area_all := l.GetProducingArea(producing_area.ParentId)
		result = append(result, producing_area_all...)
	}
	return result
}

// 递归查询所有上级酒庄
func (l *MarketingAutoAddLogic) GetChateau(id int64) []WineryBase {
	var (
		chateau WineryBase
		result  []WineryBase
	)
	l.svcCtx.DbWiki.Model(&chateau).Where("id = ?", id).Take(&chateau)
	if chateau.Id == 0 {
		return result
	}
	result = append(result, chateau)
	if chateau.ParentId != 0 {
		chateau_all := l.GetChateau(chateau.ParentId)
		result = append(result, chateau_all...)
	}
	return result
}

// 递归查询所有上级类型
func (l *MarketingAutoAddLogic) GetProductType(id int64) []ProductType {
	var (
		product_type ProductType
		result       []ProductType
	)
	l.svcCtx.DbWiki.Model(&product_type).Where("id = ?", id).Take(&product_type)
	if product_type.Id == 0 {
		return result
	}
	result = append(result, product_type)
	if product_type.Fid != 0 {
		product_type_all := l.GetProductType(product_type.Fid)
		result = append(result, product_type_all...)
	}
	return result
}
