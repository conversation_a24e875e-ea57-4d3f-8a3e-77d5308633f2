package fullreductionlabel

import (
	"encoding/json"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common"
	"fmt"
	"strconv"
	"strings"
	"time"
)

type FullreductionlabelLogic struct {
	svcCtx *svc.ServiceContext
}

func NewFullreductionlabelLogic(svcCtx *svc.ServiceContext) *FullreductionlabelLogic {
	return &FullreductionlabelLogic{
		svcCtx: svcCtx,
	}
}

type ReductionData struct {
	Id         int64
	Periods    []int64
	ActivityId []int64
	Rule       []ReductionRule
}

type ReductionRule struct {
	Id           int64
	Type         int64
	Assign       string
	Fill         float64
	Decrease     float64
	StackingNumb int64
}

type SpecialActivityGoods struct {
	ActivityId int64
	Periods    int64
}
type PeriodsSecondMerchants struct {
	Id              int64
	JoinPeriodId    int64
	JoinPeriodsType int64
}

// 查询满减标签
func (l *FullreductionlabelLogic) GetLabel(periods *[]int64, periods_class *[][]int64) *map[int64][]types.ProductLabelTypes {
	var (
		reduction_rule_info []ReductionRule
		activity_id         []int64
		any                 []map[string]interface{}
		merchants           []PeriodsSecondMerchants
	)
	PeriodsClass := *periods_class
	period_arr := *periods

	merchants_periods := make(map[int64][]int64)
	//商家秒发
	if len(PeriodsClass) == 5 && len(PeriodsClass[4]) > 0 {
		//商家秒发期数信息
		l.svcCtx.DbCommodities.
			Model(&merchants).
			Where("id IN ?", PeriodsClass[4]).
			Scan(&merchants)
		for _, v := range merchants {
			//增加期数
			if !common.InArrayInt(v.JoinPeriodId, period_arr) {
				period_arr = append(period_arr, v.JoinPeriodId)
			}
			switch v.JoinPeriodsType {
			case 0:
				if !common.InArrayInt(v.JoinPeriodId, PeriodsClass[0]) {
					PeriodsClass[0] = append(PeriodsClass[0], v.JoinPeriodId)
				}

			case 1:
				if !common.InArrayInt(v.JoinPeriodId, PeriodsClass[1]) {
					PeriodsClass[1] = append(PeriodsClass[1], v.JoinPeriodId)
				}
			}
			period_map := make([]int64, 0)
			if vv, ook := merchants_periods[v.JoinPeriodId]; ook {
				period_map = vv
			}
			//原期数对应秒发期数
			merchants_periods[v.JoinPeriodId] = append(period_map, v.Id)
		}
	}

	//查询支持满减的期数
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"filter": []map[string]interface{}{
					{
						"terms": map[string]interface{}{
							"_id": period_arr,
						},
					},
				},
				"must": []map[string]interface{}{
					{
						"match": map[string]interface{}{
							"is_support_reduction": 1,
						},
					},
				},
			},
		},
		"_source": []string{"id"},
		"from":    0,
		"size":    10000,
	}
	sql_byte, _ := json.Marshal(query)
	l.svcCtx.Es.QueryBody("periods", string(sql_byte), &any)
	period := make(map[int64]int64)
	for _, v := range any {
		if vv, ok := v["id"]; ok {
			i, _ := strconv.ParseInt(fmt.Sprintf("%v", vv), 10, 64)
			period[i] = i
		}
	}

	//期数标签
	period_label := make(map[int64][]types.ProductLabelTypes)

	timed := int64(time.Now().Unix())
	//查询开启的满减活动
	l.svcCtx.DbMarketing.Table("vh_reduction as a").
		Joins("left join vh_reduction_rule as b on a.id = b.r_id").
		Where("a.start_time <= ? and a.end_time > ? and a.is_enable = 1 and activity_type = 0", timed, timed).
		Select("a.id", "a.type", "a.assign", "b.fill", "b.decrease", "b.stacking_numb").
		Scan(&reduction_rule_info)
	//无活动直接返回
	if len(reduction_rule_info) == 0 {
		return &period_label
	}

	//满减活动数据
	reduction_data := make(map[int64]ReductionData)
	for _, v := range reduction_rule_info {
		//满减活动期数
		reduction_periods := make([]int64, 0)
		//满减活动专题活动ID
		reduction_aid := make([]int64, 0)
		switch v.Type {
		case 1: //闪购+秒发
			reduction_periods = append(reduction_periods, PeriodsClass[0]...)
			reduction_periods = append(reduction_periods, PeriodsClass[1]...)

		case 2: //闪购
			reduction_periods = append(reduction_periods, PeriodsClass[0]...)

		case 3: //秒发
			reduction_periods = append(reduction_periods, PeriodsClass[1]...)

		case 4:
			if _, ok := reduction_data[v.Id]; !ok {
				assign := strings.Split(v.Assign, ",")
				for _, vv := range assign {
					if vv != "" {
						val, _ := strconv.ParseInt(vv, 10, 64)
						//指定专题活动
						activity_id = append(activity_id, val)
						reduction_aid = append(reduction_aid, val)
					}
				}
			}

		}

		reduction_data[v.Id] = ReductionData{
			Id:         v.Id,
			Periods:    reduction_periods,
			ActivityId: reduction_aid,
			Rule:       append(reduction_data[v.Id].Rule, v),
		}
	}
	//查询专题活动期数
	activity_period_info := make(map[int64][]int64)
	if len(activity_id) > 0 {
		var activity_period_data []SpecialActivityGoods
		l.svcCtx.DbMarketing.Table("vh_special_activity_goods").
			Where("activity_id IN ? and periods IN ?", activity_id, period_arr).
			Select("activity_id", "periods").
			Scan(&activity_period_data)
		for _, v := range activity_period_data {
			activity_period_info[v.ActivityId] = append(activity_period_info[v.ActivityId], v.Periods)
		}
	}

	for _, v := range reduction_data {
		//活动匹配期数
		if len(v.ActivityId) > 0 {
			for _, vv := range v.ActivityId {
				if val, ok := activity_period_info[vv]; ok {
					v.Periods = append(v.Periods, val...)
				}
			}
		}
		//匹配期数标签
		for _, vv := range v.Periods {
			if p, ok := period[vv]; ok {
				for _, r := range v.Rule {
					title := ""
					label_id := int64(0)
					switch r.StackingNumb {
					case 1:
						label_id = 6
						title = fmt.Sprintf("%g减%g", r.Fill, r.Decrease)
					default:
						label_id = 7
						title = fmt.Sprintf("每%g减%g", float64(r.Fill), float64(r.Decrease))
					}
					period_label[p] = append(period_label[p], types.ProductLabelTypes{
						Id:    label_id,
						Type:  2,
						Title: title,
					})
				}
			}
		}
	}

	//秒发商家重新赋值
	for key, val := range period_label {
		if v, ok := merchants_periods[key]; ok {
			if len(v) > 0 {
				for _, vv := range v {
					period_label[vv] = val
				}
			}
		}
	}

	return &period_label
}
