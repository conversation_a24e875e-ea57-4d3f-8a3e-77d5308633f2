package middleware

import (
	"context"
	"encoding/base64"
	"net/http"

	"github.com/spf13/cast"
)

type GlobalMiddleware struct {
}

func NewGlobalMiddleware() *GlobalMiddleware {
	return &GlobalMiddleware{}
}

func (m *GlobalMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		uid := cast.ToInt64(r.Header.Get("vinehoo-uid"))
		vos_name := cast.ToString(r.Header.Get("vinehoo-vos-name"))
		dingtalk_uid := cast.ToString(r.Header.Get("dingtalk-uid"))
		name_byte, _ := base64.StdEncoding.DecodeString(vos_name)
		r = r.WithContext(context.WithValue(r.Context(), "vos_name", vos_name))
		r = r.WithContext(context.WithValue(r.Context(), "name", string(name_byte)))
		r = r.WithContext(context.WithValue(r.Context(), "uid", uid))
		r = r.<PERSON>ontext(context.WithValue(r.Context(), "dingtalk_uid", dingtalk_uid))

		next(w, r)
	}
}
