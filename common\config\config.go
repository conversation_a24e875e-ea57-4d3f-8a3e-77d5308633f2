package config

import (
	"engine/common/logger"
	"fmt"

	"github.com/vber/nacos/v2"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/rest"
)

type ApiConfig struct {
	rest.RestConf
	ITEM struct {
		RECOMMEND_URL             string
		ACTIVITIES_MANAGEMENT_URL string
		WIKI_URL                  string
		COMMUNITY_URL             string
		COMMODITIES_URL           string
		MARKETING_CONF_URL        string
		USER_URL                  string
		ALIURL                    string
		VMALL_URL                 string
		COMMODITIES_SERVICE_URL   string
		AUVTION_URL               string
		OPENAPI_URL               string
		WMS_DISTRIBUTE_URL        string
		USER_CACHE_URL            string
		PURCHASE_MANAGEMENT       string
		WECHART_URL               string
	}
	DATABASE_COMMODITIES MysqlCfg
	DATABASE_MARKETING   MysqlCfg
	DATABASE_NEWS        MysqlCfg
	DATABASE_COMMUNITY   MysqlCfg
	DATABASE_AUCTION     MysqlCfg
	DATABASE_USER        MysqlCfg
	DATABASE_ORDERS      MysqlCfg
	DATABASE_WMS         MysqlCfg
	DATABASE_WIKI        MysqlCfg
	FOLLOW_DATABASE      MysqlCfg
	Redis                RedisCfg
	Es                   struct {
		Host     string
		Username string
		Password string
		Prefix   string
	}
}

type MysqlCfg struct {
	Database string
	Host     string
	Port     int
	User     string
	Password string
	Prefix   string
}

type SqlsrvCfg struct {
	Host     string
	Port     int
	User     string
	Password string
	Database string
}

type RedisCfg struct {
	Host     string
	Password string
	Port     int
}

func InitApiConfig(ayn *ApiConfig, dataId, group string, operType int) {
	var data string
	defer func() {
		err := recover()
		if err != nil {
			logger.E(fmt.Sprintf("%s config init Error", dataId))
			panic(err)
		}
	}()

	data, _ = nacos.GetString(dataId, group, func(data *string, err error) {
		if err == nil {
			loadApiConfig(operType, *data, dataId, ayn)
		}
	})
	if data == "" {
		panic(fmt.Errorf("%s config is empty", dataId))
	}
	loadApiConfig(operType, data, dataId, ayn)
}

func loadApiConfig(operType int, data, dataId string, ayn *ApiConfig) {
	if operType == 0 {
		err := conf.LoadFromYamlBytes([]byte(data), ayn)
		if err != nil {
			panic(fmt.Errorf("%s config Yaml Error %s", dataId, err))
		}
	}
}

// 订单表
var OrderTable = []string{
	"vh_flash_order",
	"vh_second_order",
	"vh_cross_order",
	"vh_tail_order",
}

// 商品表
var PeriodTable = []string{
	"vh_periods_flash",
	"vh_periods_second",
	"vh_periods_cross",
	"vh_periods_leftover",
}

// 套餐表
var PackageTable = []string{
	"vh_periods_flash_set",
	"vh_periods_second_set",
	"vh_periods_cross_set",
	"vh_periods_leftover_set",
}

// 频道
var ChannelType = []string{
	"闪购",
	"秒发",
	"跨境",
	"尾货",
}
