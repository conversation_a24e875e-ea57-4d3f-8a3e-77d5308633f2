package inventory_notice

import "time"

type FictitiousGoodsData struct {
	ShortCode   string `json:"short_code"`
	BarCode     string `json:"bar_code"`
	GoodsName   string `json:"goods_name"`
	EnGoodsName string `json:"en_goods_name"`
	GoodsCount  int64  `json:"goods_count"`
}

type PeriodsProductInventory struct {
	Period      int64
	PeriodsType int64
	ProductId   int64
	ShortCode   string
}

type PeriodsData struct {
	Id           int64
	OnsaleStatus int64
	IsChannel    int64
	BuyerName    string
}

// InventoryRecord 存储每日库存记录
type WmsInventoryRecords struct {
	Id             int64 `gorm:"primaryKey"`
	ShortCode      string
	BarCode        string
	GoodsName      string
	EnGoodsName    string
	GoodsCount     int64
	OnSalePeriods  string
	YesterdaySales int64
	SevenDaySales  int64
	ThirtyDaySales int64
	Date           time.Time
	CreatedAt      int64
}

type OrderData struct {
	Id                 int64 `gorm:"primaryKey"`
	OrderQty           int64
	Period             int64
	PackageId          int64
	PaymentTime        int64
	ProductInfo        string
	AssociatedProducts string
}

type PeriodsProductInfo struct {
	ProductId int64 `json:"product_id"`
	Nums      int64 `json:"nums"`
}

type ProductSales struct {
	PaymentTime int64
	Nums        int64
}

type StorageGoods struct {
	ShortCode      string
	TaskFinishTime int64
}
