syntax = "v1"

info(
    title: "秒发营销服务"
    author: "gangh"
    email: "<EMAIL>"
    version: "v3"
)

type (
    GetFiltersListReq{
        Longitude string `form:"longitude,optional" validate:"omitempty" v:"经度"`
        Latitude string `form:"latitude,optional" validate:"omitempty" v:"纬度"`
    }

    GetFiltersListResp {
        List []GetFiltersListData `json:"list"`
    }
    GetFiltersListData {
        Title string `json:"title"`
        Identifier string `json:"identifier"`
    }

)

@server(
    middleware: Global
    group : second
    prefix :/commodities_server/v3/second
)

service main {
    @handler GetFiltersList //获取秒发筛选列表
    get /marketing/filters_list (GetFiltersListReq) returns (GetFiltersListResp)

}