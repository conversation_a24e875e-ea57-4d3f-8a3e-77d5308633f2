package data_analysis

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"sync"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/api/service/work_weixin"
	"engine/common"
	"engine/common/config"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

// 销售明细数据结构
type SalesDetailData struct {
	MainOrderNo   string  `json:"main_order_no"`
	SubOrderNo    string  `json:"sub_order_no"`
	OrderFrom     string  `json:"order_from"`
	PackageName   string  `json:"package_name"`
	PackagePrice  float64 `json:"package_price"`
	ShortCode     string  `json:"short_code"`
	ProductName   string  `json:"product_name"`
	ProductNums   int64   `json:"product_nums"`
	OrderQty      int64   `json:"order_qty"`
	PaymentTime   string  `json:"payment_time"`
	DeliveryTime  string  `json:"delivery_time"`
	CostPrice     float64 `json:"cost_price"`
	CostTotal     float64 `json:"cost_total"`
	PaymentMethod string  `json:"payment_method"`
	PaymentAmount float64 `json:"payment_amount"`
	ExpressFee    float64 `json:"express_fee"`
}

// 采购明细数据结构
type PurchaseDetailData struct {
	OrderNo       string `json:"order_no"`
	CreatedTime   string `json:"created_time"`
	Supplier      string `json:"supplier"`
	ShortCode     string `json:"short_code"`
	CnProductName string `json:"cn_product_name"`
	PurchaseNum   int64  `json:"purchase_num"`
	StorageNum    int64  `json:"storage_num"` // 萌牙入库数量
}

// 采购订单接口返回结构
type PurchaseOrderListResponse struct {
	Data struct {
		List  []PurchaseOrderList `json:"list"`
		Total int64               `json:"total"`
	} `json:"data"`
}

type PurchaseOrderList struct {
	OrderNo      string                 `json:"orderno"`
	BillDate     string                 `json:"bill_date"`
	Warehouse    string                 `json:"warehouse"`
	Supplier     string                 `json:"supplier"`
	Department   string                 `json:"department"`
	OperatorName string                 `json:"operator_name"`
	Setttlement  string                 `json:"setttlement"`
	Remark       string                 `json:"remark"`
	Items        []PurchaseOrderProduct `json:"items"`
}

type PurchaseOrderProduct struct {
	ShortCode     string      `json:"short_code"`
	BillingName   string      `json:"billing_name"`
	EnProductName string      `json:"en_product_name"`
	Unit          string      `json:"unit"`
	Capacity      string      `json:"capacity"`
	Remark        string      `json:"remark"`
	Period        interface{} `json:"period"`
	Number        interface{} `json:"number"`
	Price         interface{} `json:"price"`
	TaxRate       interface{} `json:"tax_rate"`
	Total         interface{} `json:"total"`
}

// 订单数据结构（用于查询）
type OrderDetailData struct {
	Period             int64   `json:"period"`
	MainOrderNo        string  `json:"main_order_no"`
	SubOrderNo         string  `json:"sub_order_no"`
	OrderQty           int64   `json:"order_qty"`
	PaymentTime        string  `json:"payment_time"`
	DeliveryTime       string  `json:"delivery_time"`
	PaymentAmount      float64 `json:"payment_amount"`
	PaymentMethod      int64   `json:"payment_method"`
	ExpressFee         float64 `json:"express_fee"`
	AssociatedProducts string  `json:"associated_products"`
	ProductInfo        string  `json:"product_info"`
	PackageName        string  `json:"package_name"`
	PackagePrice       float64 `json:"package_price"`
	OrderFrom          int64   `json:"order_from"`
}

// 期数产品数据结构
type PeriodProductData struct {
	Period    int64  `json:"period"`
	ProductId int64  `json:"product_id"`
	ShortCode string `json:"short_code"`
	ErpId     string `json:"erp_id"`
}

// 套餐产品数据结构
type AssociatedProductsData struct {
	ProductId int64 `json:"product_id"`
	Nums      int64 `json:"nums"`
}

type ExportOneFlowerOneWorldKanbanLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewExportOneFlowerOneWorldKanbanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ExportOneFlowerOneWorldKanbanLogic {
	return &ExportOneFlowerOneWorldKanbanLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ExportOneFlowerOneWorldKanbanLogic) ExportOneFlowerOneWorldKanban(req *types.OneFlowerOneWorldKanbanReq) error {
	dingtalk_uid := fmt.Sprintf("%v", l.ctx.Value("dingtalk_uid"))
	if dingtalk_uid == "" {
		return xerr.NewErrMsg("dingtalk_uid is empty")
	}

	// 设置默认类型
	if req.Type == 0 {
		req.Type = 1
	}

	switch req.Type {
	case 1:
		// 汇总数据导出
		return l.exportSummaryData(req, dingtalk_uid)
	case 2:
		// 销售明细导出
		return l.exportSalesDetail(req, dingtalk_uid)
	case 3:
		// 采购明细导出
		return l.exportPurchaseDetail(req, dingtalk_uid)
	default:
		return xerr.NewErrMsg("无效的导出类型")
	}
}

// 导出汇总数据
func (l *ExportOneFlowerOneWorldKanbanLogic) exportSummaryData(req *types.OneFlowerOneWorldKanbanReq, dingtalk_uid string) error {
	req.Limit = 0
	req.Page = 0

	n := NewOneFlowerOneWorldKanbanLogic(l.ctx, l.svcCtx)
	resp, err := n.ExportOneFlowerOneWorldKanban(req)
	if err != nil {
		return err
	}

	excel_data := [][]interface{}{
		{"仓库", "简码", "中文品名", "规格", "单位", "采购数量", "销售数量", "调拨出库", "调拨入库", "现库存"},
	}

	for _, v := range resp.List {
		excel_data = append(excel_data, []interface{}{
			v.Warehouse,
			v.ShortCode,
			v.CnGoodsName,
			v.Capacity,
			v.Unit,
			v.PurchaseNum,
			v.SaleNum,
			v.AllocationOutNum,
			v.AllocationInNum,
			v.InventoryNum,
		})
	}

	wx := work_weixin.NewWorkWeixinLogic(l.svcCtx)
	wx.SendFileNotice(excel_data, "一花一世界看板-汇总.xlsx", []string{dingtalk_uid})

	return nil
}

// 导出销售明细
func (l *ExportOneFlowerOneWorldKanbanLogic) exportSalesDetail(req *types.OneFlowerOneWorldKanbanReq, dingtalk_uid string) error {
	// 获取基础数据（简码和仓库编码）
	short_code, _ := l.getBaseData(req)
	if len(short_code) == 0 {
		return xerr.NewErrMsg("没有找到符合条件的数据")
	}

	// 获取销售明细数据
	salesDetails := l.getSalesDetailData(short_code)

	// 构建Excel数据
	excel_data := [][]interface{}{
		{"主订单号", "子订单号", "订单来源平台名称", "套餐名字", "套餐价格", "简码", "产品名称", "瓶数(单份套餐)", "套餐分数", "支付时间", "发货时间", "成本单价", "成本总额", "支付方式", "总额", "运费"},
	}

	for _, detail := range salesDetails {
		excel_data = append(excel_data, []interface{}{
			detail.MainOrderNo,
			detail.SubOrderNo,
			detail.OrderFrom,
			detail.PackageName,
			detail.PackagePrice,
			detail.ShortCode,
			detail.ProductName,
			detail.ProductNums,
			detail.OrderQty,
			detail.PaymentTime,
			detail.DeliveryTime,
			detail.CostPrice,
			detail.CostTotal,
			detail.PaymentMethod,
			detail.PaymentAmount,
			detail.ExpressFee,
		})
	}

	wx := work_weixin.NewWorkWeixinLogic(l.svcCtx)
	wx.SendFileNotice(excel_data, "一花一世界看板-销售明细.xlsx", []string{dingtalk_uid})

	return nil
}

// 导出采购明细
func (l *ExportOneFlowerOneWorldKanbanLogic) exportPurchaseDetail(req *types.OneFlowerOneWorldKanbanReq, dingtalk_uid string) error {
	// 构建Excel数据
	excel_data := [][]interface{}{
		{"采购订单", "单据日期", "入库仓库", "供应商", "采购部门", "采购员", "付款方式", "备注", "简码", "开票名称", "英文名", "单位", "规格", "备注", "期数", "数量", "含税单价", "税率", "价税合计", "萌牙入库数量"},
	}
	// 获取采购明细数据
	// purchaseDetails := l.getPurchaseDetailData(req)

	page := 0
	limit := 1000
	apiURL := l.svcCtx.Config.ITEM.COMMODITIES_URL + "/commodities/v3/purchaseOrderno/list"
	body := map[string]string{
		"page":              fmt.Sprintf("%d", page),
		"limit":             fmt.Sprintf("%d", limit),
		"query_type":        "2",
		"payee_merchant_id": "10",
		"push_erp_status":   "1",
	}
	if req.ShortCode != "" {
		body["short_code"] = req.ShortCode
	}
	if req.WarehouseCode != "" {
		body["warehouse_code"] = req.WarehouseCode
	}
	for {
		var (
			order_list PurchaseOrderListResponse
			orderNos   []string
		)
		page++
		body["page"] = fmt.Sprintf("%d", page)
		res, err := l.svcCtx.HttpClient.Get(apiURL, body, map[string]string{})
		json.Unmarshal(res.Body(), &order_list)
		if err != nil || len(order_list.Data.List) == 0 {
			break
		}
		for _, v := range order_list.Data.List {
			orderNos = append(orderNos, v.OrderNo)
		}
		storageMap := l.getStorageNumbers(orderNos)

		for _, v := range order_list.Data.List {
			for _, item := range v.Items {
				// 获取萌牙入库数量
				storageKey := fmt.Sprintf("%s:%s", v.OrderNo, item.ShortCode)
				storageNum := int64(0)
				if num, exists := storageMap[storageKey]; exists {
					storageNum = num
				}
				excel_data = append(excel_data, []interface{}{
					v.OrderNo,
					v.BillDate,
					v.Warehouse,
					v.Supplier,
					v.Department,
					v.OperatorName,
					v.Setttlement,
					v.Remark,
					item.ShortCode,
					item.BillingName,
					item.EnProductName,
					item.Unit,
					item.Capacity,
					item.Remark,
					item.Period,
					item.Number,
					item.Price,
					item.TaxRate,
					item.Total,
					storageNum,
				})
			}
		}

		// 总页数
		total_page := math.Ceil(common.Bcdev(float64(order_list.Data.Total), float64(req.Limit), 2))
		if page >= int(total_page) {
			break
		}
	}
	wx := work_weixin.NewWorkWeixinLogic(l.svcCtx)
	wx.SendFileNotice(excel_data, "一花一世界看板-采购明细.xlsx", []string{dingtalk_uid})

	return nil
}

// 获取基础数据（简码和仓库编码）
func (l *ExportOneFlowerOneWorldKanbanLogic) getBaseData(req *types.OneFlowerOneWorldKanbanReq) ([]string, []string) {
	// 构建基础查询
	query := l.svcCtx.DbCommodities.Table("vh_purchase_orderno as po").
		Joins("LEFT JOIN vh_purchase_orderno_items as poi ON po.id = poi.purchase_orderno_id").
		Joins("LEFT JOIN vh_wiki.vh_products as p ON poi.short_code = p.short_code COLLATE utf8mb4_croatian_ci").
		Where("po.corp_code = ? AND po.operate_status = ?", "032", 1)

	// 添加可选条件
	if req.ShortCode != "" {
		query = query.Where("poi.short_code = ?", req.ShortCode)
	}
	if req.WarehouseCode != "" {
		query = query.Where("po.warehouse_code = ?", req.WarehouseCode)
	}

	var result []struct {
		ShortCode     string `gorm:"column:short_code"`
		WarehouseCode string `gorm:"column:warehouse_code"`
	}

	query.Select("DISTINCT poi.short_code, po.warehouse_code").Scan(&result)

	var short_code []string
	var warehouse_code []string

	for _, item := range result {
		if !common.InArrayStr(item.ShortCode, short_code) {
			short_code = append(short_code, item.ShortCode)
		}
		if !common.InArrayStr(item.WarehouseCode, warehouse_code) {
			warehouse_code = append(warehouse_code, item.WarehouseCode)
		}
	}

	return short_code, warehouse_code
}

// 获取销售明细数据
func (l *ExportOneFlowerOneWorldKanbanLogic) getSalesDetailData(short_code []string) []SalesDetailData {
	var (
		lc     sync.Mutex
		wg     sync.WaitGroup
		result []SalesDetailData
	)

	// 支付方式映射
	paymentMethodMap := map[int64]string{
		0:   "支付宝APP",
		1:   "支付宝H5",
		2:   "支付宝PC",
		3:   "微信APP",
		4:   "微信小程序",
		5:   "微信H5",
		6:   "抖音支付宝",
		7:   "微信JSAPI(公众号支付)",
		8:   "抖音微信",
		9:   "微信扫码",
		10:  "-线上电汇",
		11:  "-线下转账",
		201: "兔头",
		202: "礼品卡",
	}

	// 订单来源映射
	orderFromMap := map[int64]string{
		0:  "闪购",
		1:  "秒发",
		2:  "跨境",
		3:  "尾货",
		4:  "兔头商城",
		7:  "三方",
		8:  "线下",
		9:  "商家秒发",
		10: "商家闪购",
		11: "拍卖",
	}

	for k, period_table := range config.PeriodTable {
		wg.Add(1)
		go func(k int, period_table string) {
			defer wg.Done()

			var (
				orderData  []OrderDetailData
				periodData []PeriodProductData
				periodIds  []int64
			)

			order_table := config.OrderTable[k]
			package_table := config.PackageTable[k]

			// 查询期数和产品
			l.svcCtx.DbCommodities.Table(period_table+" as p").
				Joins("left join vh_periods_product_inventory as poi on poi.period = p.id").
				Where("p.payee_merchant_id = 10 and poi.short_code IN ?", short_code).
				Select("poi.period,poi.product_id,poi.short_code,poi.erp_id").
				Scan(&periodData)

			if len(periodData) == 0 {
				return
			}

			periodDataMap := make(map[string]PeriodProductData)
			for _, v := range periodData {
				key := fmt.Sprintf("%d:%d", v.Period, v.ProductId)
				periodDataMap[key] = v
				periodIds = append(periodIds, v.Period)
			}

			// 查询订单信息
			l.svcCtx.DbCommodities.Table("vh_orders."+order_table+" as o").
				Joins(fmt.Sprintf("left join vh_commodities.%s as pkg on pkg.id = o.package_id", package_table)).
				Joins("left join vh_orders.vh_order_main as om on om.id = o.main_order_id").
				Joins("left join vh_orders.vh_order_mystery_box_log as box on box.main_order_no = om.main_order_no").
				Where("o.period IN ? and o.sub_order_status IN(1,2,3)", periodIds).
				Select("o.period,om.main_order_no,o.sub_order_no,o.order_qty,o.payment_time,o.delivery_time,o.payment_amount,om.payment_method,o.express_fee,pkg.associated_products,box.product_info,pkg.package_name,pkg.price as package_price,o.order_from").
				Scan(&orderData)

			for _, order := range orderData {
				var pkgProducts []AssociatedProductsData
				if order.ProductInfo != "" {
					json.Unmarshal([]byte(order.ProductInfo), &pkgProducts)
				} else {
					json.Unmarshal([]byte(order.AssociatedProducts), &pkgProducts)
				}

				for _, p := range pkgProducts {
					key := fmt.Sprintf("%d:%d", order.Period, p.ProductId)
					if periodInfo, ok := periodDataMap[key]; ok {
						lc.Lock()

						// 获取产品信息
						var productInfo struct {
							CnProductName string  `gorm:"column:cn_product_name"`
							Costprice     float64 `gorm:"column:costprice"`
						}
						l.svcCtx.DbWiki.Table("vh_products").
							Where("short_code = ?", periodInfo.ShortCode).
							Select("cn_product_name, costprice").
							Scan(&productInfo)

						salesDetail := SalesDetailData{
							MainOrderNo:   order.MainOrderNo,
							SubOrderNo:    order.SubOrderNo,
							OrderFrom:     orderFromMap[order.OrderFrom],
							PackageName:   order.PackageName,
							PackagePrice:  order.PackagePrice,
							ShortCode:     periodInfo.ShortCode,
							ProductName:   productInfo.CnProductName,
							ProductNums:   p.Nums,
							OrderQty:      order.OrderQty,
							PaymentTime:   l.formatTimestamp(order.PaymentTime),
							DeliveryTime:  l.formatTimestamp(order.DeliveryTime),
							CostPrice:     productInfo.Costprice,
							CostTotal:     productInfo.Costprice * float64(p.Nums*order.OrderQty),
							PaymentMethod: paymentMethodMap[order.PaymentMethod],
							PaymentAmount: order.PaymentAmount,
							ExpressFee:    order.ExpressFee,
						}

						result = append(result, salesDetail)
						lc.Unlock()
					}
				}
			}
		}(k, period_table)
	}
	wg.Wait()

	return result
}

// 获取采购明细数据
func (l *ExportOneFlowerOneWorldKanbanLogic) getPurchaseDetailData(req *types.OneFlowerOneWorldKanbanReq) []PurchaseDetailData {
	// 构建基础查询
	query := l.svcCtx.DbCommodities.Table("vh_purchase_orderno as po").
		Joins("LEFT JOIN vh_purchase_orderno_items as poi ON po.id = poi.purchase_orderno_id").
		Joins("LEFT JOIN vh_wiki.vh_products as p ON poi.short_code = p.short_code COLLATE utf8mb4_croatian_ci").
		Where("po.corp_code = ? AND po.operate_status = ?", "032", 1)

	// 添加可选条件
	if req.ShortCode != "" {
		query = query.Where("poi.short_code = ?", req.ShortCode)
	}
	if req.WarehouseCode != "" {
		query = query.Where("po.warehouse_code = ?", req.WarehouseCode)
	}

	var result []PurchaseQueryData

	query.Select("po.orderno, po.created_time, po.supplier, poi.short_code, p.cn_product_name, CAST(poi.number AS SIGNED) as number").
		Order("po.created_time DESC").
		Scan(&result)

	// 提取所有采购单号
	var orderNos []string
	for _, item := range result {
		if !common.InArrayStr(item.OrderNo, orderNos) {
			orderNos = append(orderNos, item.OrderNo)
		}
	}
	// 获取萌牙入库数量映射
	storageMap := l.getStorageNumbers(orderNos)

	var purchaseDetails []PurchaseDetailData
	for _, item := range result {
		createdTime := ""
		if item.CreatedTime > 0 {
			createdTime = time.Unix(item.CreatedTime, 0).Format("2006-01-02 15:04:05")
		}

		// 获取萌牙入库数量
		storageKey := fmt.Sprintf("%s:%s", item.OrderNo, item.ShortCode)
		storageNum := int64(0)
		if num, exists := storageMap[storageKey]; exists {
			storageNum = num
		}

		purchaseDetails = append(purchaseDetails, PurchaseDetailData{
			OrderNo:       item.OrderNo,
			CreatedTime:   createdTime,
			Supplier:      item.Supplier,
			ShortCode:     item.ShortCode,
			CnProductName: item.CnProductName,
			PurchaseNum:   item.PurchaseNum,
			StorageNum:    storageNum,
		})
	}

	return purchaseDetails
}

// 采购数据结构（用于查询萌牙入库数量）
type PurchaseQueryData struct {
	OrderNo       string `gorm:"column:orderno"`
	CreatedTime   int64  `gorm:"column:created_time"`
	Supplier      string `gorm:"column:supplier"`
	ShortCode     string `gorm:"column:short_code"`
	CnProductName string `gorm:"column:cn_product_name"`
	PurchaseNum   int64  `gorm:"column:number"`
}

// 获取萌牙入库数量
func (l *ExportOneFlowerOneWorldKanbanLogic) getStorageNumbers(orderNos []string) map[string]int64 {
	// 查询萌牙入库数量
	var storageResult []struct {
		RdCode    string `gorm:"column:rd_code"`
		ShortCode string `gorm:"column:short_code"`
		Number    int64  `gorm:"column:number"`
	}

	l.svcCtx.DbWms.Table("wms_storage as s").
		Joins("LEFT JOIN wms_storage_goods as sg ON sg.storage_id = s.storage_id").
		Joins("LEFT JOIN wms_storage_location as sl ON sl.storage_goods_id = sg.storage_goods_id").
		Where("s.rd_code IN ?", orderNos).
		Select("s.rd_code, sg.short_code, COALESCE(SUM(sl.number), 0) as number").
		Group("s.rd_code, sg.short_code").
		Scan(&storageResult)

	// 构建映射
	storageMap := make(map[string]int64)
	for _, item := range storageResult {
		key := fmt.Sprintf("%s:%s", item.RdCode, item.ShortCode)
		storageMap[key] = item.Number
	}

	return storageMap
}

// 格式化时间戳为指定格式 (2025-6-24 11:07:56)
func (l *ExportOneFlowerOneWorldKanbanLogic) formatTimestamp(timeStr string) string {
	if timeStr == "" {
		return ""
	}

	// 尝试将字符串转换为时间戳
	timestamp, err := strconv.ParseInt(timeStr, 10, 64)
	if err != nil {
		// 如果不是时间戳，尝试解析为标准时间格式
		t, parseErr := time.Parse("2006-01-02 15:04:05", timeStr)
		if parseErr != nil {
			// 如果都解析失败，返回原字符串
			return timeStr
		}
		// 格式化为 2025-6-24 11:07:56 格式
		return t.Format("2006-1-2 15:04:05")
	}

	// 将时间戳转换为时间对象并格式化
	t := time.Unix(timestamp, 0)
	return t.Format("2006-1-2 15:04:05")
}
