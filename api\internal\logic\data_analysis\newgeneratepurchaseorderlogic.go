package data_analysis

import (
	"context"

	"engine/api/command/purchase_order"
	"engine/api/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type NewGeneratePurchaseOrderLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewNewGeneratePurchaseOrderLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NewGeneratePurchaseOrderLogic {
	return &NewGeneratePurchaseOrderLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *NewGeneratePurchaseOrderLogic) NewGeneratePurchaseOrder() error {
	ser := purchase_order.NewPurchaseOrderLogicStruct(
		l.svcCtx.DbCommodities,
		l.svcCtx.DbOrders,
		l.svcCtx.DbWms,
		l.svcCtx.Config,
		l.svcCtx.RDbSix,
	)
	go ser.Execute()

	return nil
}
