package second

import (
	"engine/api/internal/logic/second"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
	"net/http"
)

func EditFiltersGoodsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.EditFiltersGoodsReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := second.NewEditFiltersGoodsLogic(r.Context(), svcCtx)
		err := l.EditFiltersGoods(&req)
		result.HttpResult(r, w, result.Null<PERSON>son{}, err)
	}
}
