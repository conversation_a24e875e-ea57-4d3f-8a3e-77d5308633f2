package second_home

import (
	"context"
	"encoding/json"
	"fmt"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
)

type AggregationV2Logic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAggregationV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *AggregationV2Logic {
	return &AggregationV2Logic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AggregationV2Logic) AggregationV2(req *types.AggregationV2Req) (resp *types.AggregationV2Resp, err error) {
	// 如果 client 等于 5，则设置为 1
	if req.Client == 5 {
		req.Client = 1
	}

	result := types.AggregationV2Resp{
		Column: make([]map[string]interface{}, 0),
		Card:   make([]map[string]interface{}, 0),
	}
	resp = &result

	mr.Finish(func() (err error) {
		//获取栏目列表
		result.Column = l.getColumns(req)
		return
	}, func() (err error) {
		//获取卡片列表
		result.Card = l.getCard()
		return
	})

	return
}

// 获取卡片列表
func (l *AggregationV2Logic) getCard() []map[string]interface{} {
	var (
		header    map[string]string
		home_data SecondHome
	)
	result := make([]map[string]interface{}, 0)
	url := l.svcCtx.Config.ITEM.MARKETING_CONF_URL + "/marketing-conf/v3/card/mf_clientlist"
	body := map[string]string{
		"channel": "2",
	}
	res, err := l.svcCtx.HttpClient.Get(url, body, header)
	if err != nil {
		return result
	}
	json.Unmarshal(res.Body(), &home_data)

	if len(home_data.Data.List) > 0 {
		result = home_data.Data.List
	}

	return result
}

// 获取栏目列表
func (l *AggregationV2Logic) getColumns(req *types.AggregationV2Req) []map[string]interface{} {
	var (
		header    map[string]string
		home_data SecondHome
	)
	result := make([]map[string]interface{}, 0)
	url := l.svcCtx.Config.ITEM.MARKETING_CONF_URL + "/marketing-conf/v3/column/clientlist"
	body := map[string]string{
		"channel": "2",
		"client":  fmt.Sprintf("%d", req.Client),
	}
	res, err := l.svcCtx.HttpClient.Get(url, body, header)
	if err != nil {
		return result
	}
	json.Unmarshal(res.Body(), &home_data)

	if len(home_data.Data.List) > 0 {
		result = home_data.Data.List
	}

	return result
}
