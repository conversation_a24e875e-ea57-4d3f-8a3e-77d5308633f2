package automatic_failure

import (
	"engine/common/config"
	"engine/common/httpClient"
	"fmt"
	"sync"
	"time"

	"gorm.io/gorm"
)

type AutomaticFailureLogic struct {
	Config     config.ApiConfig
	Db         *gorm.DB
	HttpClient *httpClient.HttpConfig
}

/*定时对处于【待上架、待绑定】，且创建时间大于 24*30 小时且没有【二次确认上架】的期数进行自动失效*/
func NewAutomaticFailureLogic(db *gorm.DB, Config config.ApiConfig) {
	l := &AutomaticFailureLogic{
		Config:     Config,
		Db:         db,
		HttpClient: httpClient.Handle(Config),
	}
	// 运行
	go l.Run()
}

// 运行
func (l *AutomaticFailureLogic) Run() {
	for {
		timeN := time.Now()

		// 每天5点执行
		hour := timeN.Hour()
		minute := timeN.Minute()
		// second := timeN.Second()
		if hour == 5 && minute == 0 {
			fmt.Println("自动失效--ok")
			// 执行处理
			l.Execute()
		}
		fmt.Println("自动失效--no")
		// 每隔1分钟执行一次
		time.Sleep(1 * time.Minute)
	}
}

// 执行处理
func (l *AutomaticFailureLogic) Execute() {
	var wg sync.WaitGroup
	//定时对处于【待上架、待绑定】，且创建时间大于 24*30 小时且没有【二次确认上架】的期数进行自动失效
	// 获取当前时间
	currentTime := time.Now()
	// 计算 24*30 小时前的时间
	// 30 天 = 24 小时 * 30
	thirtyDaysAgo := currentTime.Add(-24 * 30 * time.Hour)
	// 将时间转换为时间戳（秒）
	timestamp := thirtyDaysAgo.Unix()

	for _, v := range config.PeriodTable {
		wg.Add(1)
		go func(table_name string) {
			defer wg.Done()
			var (
				info      []PeriodsInfo
				period_id []int64
			)
			// 查询创建时间大于 24*30 小时且没有【二次确认上架】的期数
			l.Db.Table(table_name).
				Where("created_time < ? and onsale_status = 0 and onsale_verify_status = 0 and is_fail = 1", timestamp).
				Scan(&info)
			for _, v := range info {
				period_id = append(period_id, v.Id)
			}
			if len(period_id) > 0 {
				l.Db.Table(table_name).Where("id IN ?", period_id).Update("is_fail", 0)
			}
		}(v)
	}
	wg.Wait()

}
