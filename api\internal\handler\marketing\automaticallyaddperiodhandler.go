package marketing

import (
	"engine/api/internal/logic/marketing"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
	"net/http"
)

func AutomaticallyAddPeriodHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AutomaticallyAddPeriodReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := marketing.NewAutomaticallyAddPeriodLogic(r.Context(), svcCtx)
		err := l.AutomaticallyAddPeriod(&req)
		result.HttpResult(r, w, result.NullJson{}, err)
	}
}
