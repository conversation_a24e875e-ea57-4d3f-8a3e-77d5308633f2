package second

import (
	"context"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddFiltersLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddFiltersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddFiltersLogic {
	return &AddFiltersLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddFiltersLogic) AddFilters(req *types.AddFiltersReq) error {
	var count int64
	//操作人ID
	uid := l.ctx.Value("uid").(int64)
	//操作人
	user_name := l.ctx.Value("name").(string)
	//查询标签和标识是否存在
	l.svcCtx.DbCommodities.
		Model(&PeriodsSecondFilters{}).
		Where("title = ?", req.Title).
		Count(&count)
	if count > 0 {
		return xerr.NewParamErrMsg("标签标题或标签标识已存在")
	}

	timed := int64(time.Now().Unix())
	info := map[string]interface{}{
		"filters_type": 1,
		"status":       0,
		"title":        req.Title,
		"identifier":   "commoditieslist",
		"weight_value": req.WeightValue,
		"createdor_id": uid,
		"createdor":    user_name,
		"operator_id":  uid,
		"operator":     user_name,
		"created_time": timed,
		"update_time":  timed,
	}
	err := l.svcCtx.DbCommodities.Model(&PeriodsSecondFilters{}).Create(info).Error
	if err != nil {
		return xerr.NewParamErrMsg(err.Error())
	}

	return nil
}
