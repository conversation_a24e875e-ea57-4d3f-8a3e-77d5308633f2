package period_movesales_notice

import (
	"encoding/json"
	"engine/api/internal/svc"
	"engine/api/service/work_weixin"
	"engine/common"
	"engine/common/config"
	"engine/common/esClient"
	"engine/common/xtime"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"
)

type PeriodMovesalesNoticeLogic struct {
	SvcCtx *svc.ServiceContext
}

var (
	userid = []string{"BaiRiMengXiangJia", "LongFei", "ChenHongZhou", "YuMengYang", "ZhuChengHao", "LiXiangDian"}
)

func NewPeriodMovesalesNoticeLogic(svcCtx *svc.ServiceContext) *PeriodMovesalesNoticeLogic {
	// 修改测试环境通知人
	if !strings.Contains(svcCtx.Config.ITEM.ALIURL, "vinehoo") {
		userid = []string{"GanGaoHan", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}
	}

	return &PeriodMovesalesNoticeLogic{
		SvcCtx: svcCtx,
	}
}

// 导出全频道在售库存动销，以表格形式每天早上7点30推送到企微中台【杨文科、龙飞、陈泓州】：https://devops.aliyun.com/projex/project/8ab3146db7bf4a79876e3a5333/task#viewIdentifier=1945bcda00c0cf935ebdd8a5&openWorkitemIdentifier=cc0d11805a37ae131ccfae6caa
func (l *PeriodMovesalesNoticeLogic) Execute() {
	var (
		hour   int
		minute int
	)
	// 测试环境不通知
	if !strings.Contains(l.SvcCtx.Config.ITEM.ALIURL, "vinehoo") {
		return
	}

	for {
		hour = time.Now().Hour()
		minute = time.Now().Minute()

		// 以表格形式每天早上7点30推送到企微中台【杨文科、龙飞、陈泓州】
		if hour == 7 && minute == 30 {
			fmt.Println("全频道在售库存动销--start")
			l.Handle()
		}
		fmt.Println("全频道在售库存动销--end")
		// 延迟
		time.Sleep(1 * time.Minute)
	}
}

func (l *PeriodMovesalesNoticeLogic) Handle() {
	var (
		fictitious_goods          []FictitiousGoodsData
		periods_product_inventory []PeriodsProductInventory
		wg                        sync.WaitGroup
		all_periods_data          []PeriodsData
		period_ids                []int64
		warehouse_ids             []string
		short_codes               []string
		order_data_arr            []OrderData
	)

	// excel数据
	excel_data := [][]interface{}{
		{"频道", "期数", "简码", "标题", "上架日期", "总销售瓶数", "昨天销售瓶数", "7天销售瓶数", "30天销售瓶数", "库存数量", "浏览量", "转化率", "采购"},
	}
	// 设置昨天0点时间戳
	yesterday := xtime.GetDayStartTimestamp(1)
	// 设置7天前0点时间戳
	sevenDaysAgo := xtime.GetDayStartTimestamp(7)
	// 设置30天前0点时间戳
	thirtyDaysAgo := xtime.GetDayStartTimestamp(30)
	// 转换今天为时间戳
	todayUnix := xtime.GetDayStartTimestamp(0)

	for k, v := range config.PeriodTable {
		wg.Add(1)
		go func(k int, v string) {
			defer wg.Done()
			var (
				periods_data []PeriodsData
				s_period_ids []int64
				order_data   []OrderData
			)
			// 查询在售期数
			l.SvcCtx.DbCommodities.Table(v).Where("onsale_status = 2").
				Select("id,title,is_channel,onsale_time,buyer_name,pageviews").
				Scan(&periods_data)
			if len(periods_data) == 0 {
				return
			}

			for _, v := range periods_data {
				s_period_ids = append(s_period_ids, v.Id)
				v.PeriodsType = config.ChannelType[k]
				all_periods_data = append(all_periods_data, v)
			}
			period_ids = append(period_ids, s_period_ids...)

			order_table := config.OrderTable[k]
			package_table := config.PackageTable[k]
			// 查询销量
			l.SvcCtx.DbOrders.Table(order_table+" o").
				Joins("left join vh_order_main om on om.id = o.main_order_id").
				Joins("left join vh_order_mystery_box_log box on box.main_order_no = om.main_order_no").
				Joins(fmt.Sprintf("left join vh_commodities.%s pkg on pkg.id = o.package_id", package_table)).
				Where("o.period IN ? and o.payment_time < ? and o.sub_order_status IN(1,2,3)", s_period_ids, todayUnix).
				Select("o.id,o.order_qty,o.period,o.package_id,o.payment_time,box.product_info,pkg.associated_products").
				Scan(&order_data)
			order_data_arr = append(order_data_arr, order_data...)

		}(k, v)
	}
	wg.Wait()

	if len(all_periods_data) > 0 {
		// 查询ES信息
		es_info := l.GetPeriods(period_ids)
		period_es_map := make(map[int64]EsPeriodsInfo)
		for _, v := range es_info {
			period_es_map[v.Id] = v
		}

		// 销量处理
		product_sales := make(map[string][]ProductSales)
		for _, o := range order_data_arr {
			var product_info []PeriodsProductInfo
			if o.ProductInfo != "" {
				json.Unmarshal([]byte(o.ProductInfo), &product_info)
			} else {
				json.Unmarshal([]byte(o.AssociatedProducts), &product_info)
			}
			for _, p := range product_info {
				// 销售瓶数
				number := p.Nums * o.OrderQty
				key := fmt.Sprintf("%d:%d", o.Period, p.ProductId)
				if _, ok := product_sales[key]; !ok {
					product_sales[key] = make([]ProductSales, 0)
				}
				product_sales[key] = append(product_sales[key], ProductSales{
					PaymentTime: o.PaymentTime,
					Nums:        number,
				})
			}
		}

		// 查询期数产品
		l.SvcCtx.DbCommodities.Model(PeriodsProductInventory{}).
			Where("period IN ? and is_use_comment = 0", period_ids).
			Scan(&periods_product_inventory)
		periods_product := make(map[int64][]PeriodsProductInventory)
		for _, v := range periods_product_inventory {
			if !common.InArrayStr(v.WarehouseId, warehouse_ids) {
				warehouse_ids = append(warehouse_ids, v.WarehouseId)
			}
			if !common.InArrayStr(v.ShortCode, short_codes) {
				short_codes = append(short_codes, v.ShortCode)
			}
			periods_product[v.Period] = append(periods_product[v.Period], v)
		}

		// 查询萌牙库存
		l.SvcCtx.DbWms.Table("wms_fictitious_goods fg").
			Joins("left join wms_goods g on fg.bar_code = g.bar_code").
			Where("fg.fictitious_id IN ? and g.short_code IN ?", warehouse_ids, short_codes).
			Select("fg.fictitious_id,g.short_code,fg.bar_code,g.goods_name,g.en_goods_name,sum(fg.goods_count) as goods_count").
			Group("fg.fictitious_id,g.short_code").
			Scan(&fictitious_goods)
		wms_goods_count_map := make(map[string]int64)
		for _, v := range fictitious_goods {
			wms_goods_count_map[fmt.Sprintf("%d:%s", v.FictitiousId, v.ShortCode)] = v.GoodsCount
		}
		//组装数据
		for _, v := range all_periods_data {
			var (
				s_periods_product []PeriodsProductInventory
				purchased_person  int64 = 0 // 期数真实已售
				conversion_rate   int64 = 0 // 转化率
			)
			// 期数产品
			if val, ok := periods_product[v.Id]; ok {
				s_periods_product = val
			}

			// 期数真实已售
			if val, ok := period_es_map[v.Id]; ok {
				purchased_person = val.PurchasedPerson
			}
			// 转化率
			if purchased_person > 0 && v.Pageviews > 0 {
				conversion_rate = (purchased_person * 1000) / v.Pageviews
			}

			for _, product := range s_periods_product {
				var (
					s_product_sales []ProductSales
					totalSales      int64 = 0 // 总销售瓶数
					inventory_nums  int64 = 0 //库存数量
					yesterdaySales  int64 = 0 // 昨日动销
					sevenDaysSales  int64 = 0 // 7日动销
					thirtyDaysSales int64 = 0 // 30日动销
				)

				//销售瓶数
				key := fmt.Sprintf("%d:%d", v.Id, product.ProductId)
				if val, ok := product_sales[key]; ok {
					s_product_sales = val
				}
				for _, s := range s_product_sales {
					totalSales = totalSales + s.Nums
					if s.PaymentTime >= yesterday { // 昨日动销
						yesterdaySales = s.Nums + yesterdaySales
					}
					if s.PaymentTime >= sevenDaysAgo { // 7日动销
						sevenDaysSales = s.Nums + sevenDaysSales
					}
					if s.PaymentTime >= thirtyDaysAgo { // 30日动销
						thirtyDaysSales = s.Nums + thirtyDaysSales
					}
				}

				// 库存
				if v.PeriodsType != "跨境" {
					// 虚拟仓ID
					fictitious_id, _ := strconv.ParseInt(product.WarehouseId, 10, 64)
					if val, ok := wms_goods_count_map[fmt.Sprintf("%d:%s", fictitious_id, product.ShortCode)]; ok {
						inventory_nums = val
					}
				} else {
					inventory_nums = product.Inventory
				}

				// 期数
				period_str := fmt.Sprintf("%d", v.Id)
				if v.IsChannel == 1 {
					period_str = fmt.Sprintf("%s（渠道）", period_str)
				}

				// 添加到Excel数据
				excel_data = append(excel_data, []interface{}{
					v.PeriodsType,
					period_str,
					product.ShortCode,
					v.Title,
					xtime.DateFormat("2006-01-02", v.OnsaleTime),
					totalSales,
					yesterdaySales,
					sevenDaysSales,
					thirtyDaysSales,
					inventory_nums,
					v.Pageviews,
					fmt.Sprintf("%d‰", conversion_rate),
					v.BuyerName,
				})
			}

		}
	}

	weixin := work_weixin.NewWorkWeixinLogic(l.SvcCtx)
	weixin.SendFileNotice(excel_data, "全频道在售库存动销.xlsx", userid)
}

// 获取期数ES信息
func (l *PeriodMovesalesNoticeLogic) GetPeriods(period_ids []int64) []EsPeriodsInfo {
	var result []EsPeriodsInfo

	Es := esClient.New(l.SvcCtx.Config)
	where := [][]interface{}{
		{"_id", "in", period_ids},
	}

	Es.Name("periods").Where(where).
		Field([]string{
			"id",
			"purchased_person",
		}).Select(&result)

	return result
}
