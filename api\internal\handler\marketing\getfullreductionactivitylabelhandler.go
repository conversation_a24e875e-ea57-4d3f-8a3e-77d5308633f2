package marketing

import (
	"engine/api/internal/logic/marketing"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func GetFullReductionActivityLabelHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetFullReductionActivityLabelReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := marketing.NewGetFullReductionActivityLabelLogic(r.Context(), svcCtx)
		resp, err := l.GetFullReductionActivityLabel(&req)
		result.HttpResult(r, w, resp, err)
	}
}
