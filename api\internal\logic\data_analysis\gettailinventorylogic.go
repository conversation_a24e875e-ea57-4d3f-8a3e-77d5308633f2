package data_analysis

import (
	"context"
	"encoding/json"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/xtime"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetTailInventoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetTailInventoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTailInventoryLogic {
	return &GetTailInventoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetTailInventoryLogic) GetTailInventory(req *types.GetTailInventoryReq) (resp *types.GetTailInventoryResp, err error) {
	var (
		info  []TailInventoryWms
		count int64
	)
	result := types.GetTailInventoryResp{
		List:  make([]types.GetTailInventoryRespList, 0),
		Total: 0,
	}
	resp = &result

	//分页
	offset := int((req.Page - 1) * req.Limit)

	//获取今日0点时间戳
	stime := xtime.TodayTimeStamp()

	query := l.svcCtx.DbCommodities.Model(&TailInventoryWms{}).Where("created_time >= ?", stime)
	// 简码
	if req.ShortCode != "" {
		query = query.Where("short_code = ?", req.ShortCode)
	}
	// 无在售
	if req.NotSale == 1 {
		query = query.Where("is_sale = 0")
	}
	query.Count(&count)
	query.Offset(offset).Limit(int(req.Limit)).Scan(&info)

	for _, v := range info {
		s_inventory := make([]map[string]interface{}, 0)
		s_period_info := make([]map[string]interface{}, 0)
		s_remaining_sale_inventory := make([]map[string]interface{}, 0)
		// 库存
		json.Unmarshal([]byte(v.Inventory), &s_inventory)
		// 期数信息
		if v.PeriodInfo != "" && v.PeriodInfo != "{}" {
			json.Unmarshal([]byte(v.PeriodInfo), &s_period_info)
		}
		// 在售剩余售卖库存
		if v.RemainingSaleInventory != "" {
			json.Unmarshal([]byte(v.RemainingSaleInventory), &s_remaining_sale_inventory)
		}

		result.List = append(result.List, types.GetTailInventoryRespList{
			Id:                     v.Id,
			CnGoodsName:            v.CnGoodsName,
			EnGoodsName:            v.EnGoodsName,
			ShortCode:              v.ShortCode,
			BarCode:                v.BarCode,
			Inventory:              s_inventory,
			IsSale:                 v.IsSale,
			Period:                 v.Period,
			PeriodInfo:             s_period_info,
			RemainingSaleInventory: s_remaining_sale_inventory,
			CreatedTime:            xtime.Date(v.CreatedTime),
		})
	}
	result.Total = count

	return
}
