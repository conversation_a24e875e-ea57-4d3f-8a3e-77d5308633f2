package second

import (
	"context"
	"sync"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddFiltersGoodsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddFiltersGoodsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddFiltersGoodsLogic {
	return &AddFiltersGoodsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddFiltersGoodsLogic) AddFiltersGoods(req *types.AddFiltersGoodsReq) error {
	var (
		filters_info PeriodsSecondFilters
		goods_count  int64
		table_name   string
		periods_info PeriodsData
		wg           sync.WaitGroup
	)
	if req.PeriodsType != 1 {
		return xerr.NewParamErrMsg("只能添加秒发商品")
	}
	// 操作人ID
	uid := l.ctx.Value("uid").(int64)
	// 操作人
	user_name := l.ctx.Value("name").(string)
	wg.Add(1)
	go func() {
		defer wg.Done()
		// 查询标签和标识是否存在
		l.svcCtx.DbCommodities.Model(&filters_info).Where("id = ?", req.FiltersId).Take(&filters_info)
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		// 查询商品信息数量
		l.svcCtx.DbCommodities.
			Model(&PeriodsSecondFiltersGoods{}).
			Where("filters_id = ? and periods = ?", req.FiltersId, req.Periods).
			Count(&goods_count)
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		// 查询期数信息
		switch req.PeriodsType {
		case 0: // 闪购
			table_name = "vh_periods_flash"
		case 1: // 秒发
			table_name = "vh_periods_second"
		}
		l.svcCtx.DbCommodities.
			Table(table_name).
			Where("id = ?", req.Periods).
			Select("id", "onsale_status").
			Scan(&periods_info)
	}()
	wg.Wait()
	if filters_info.Id == 0 {
		return xerr.NewParamErrMsg("筛选标签不存在")
	}
	if filters_info.FiltersType != 1 {
		return xerr.NewParamErrMsg("标签类型不支持添加商品")
	}
	if goods_count > 0 {
		return xerr.NewParamErrMsg("期数已添加")
	}
	if periods_info.Id == 0 {
		return xerr.NewParamErrMsg("期数不存在")
	}
	if periods_info.OnsaleStatus != 2 {
		return xerr.NewParamErrMsg("只能添加在售中的期数")
	}
	timed := int64(time.Now().Unix())

	goods := map[string]interface{}{
		"periods":          req.Periods,
		"periods_type":     req.PeriodsType,
		"goods_short_name": req.GoodsShortName,
		"filters_id":       req.FiltersId,
		"status":           req.Status,
		"onsale_status":    2,
		"weight_value":     req.WeightValue,
		"created_id":       uid,
		"created_name":     user_name,
		"created_time":     timed,
		"update_id":        uid,
		"update_name":      user_name,
		"update_time":      timed,
	}
	err := l.svcCtx.DbCommodities.Model(&PeriodsSecondFiltersGoods{}).Create(goods).Error
	if err != nil {
		return xerr.NewParamErrMsg(err.Error())
	}

	return nil
}
