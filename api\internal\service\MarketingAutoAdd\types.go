package MarketingAutoAdd

type Reduction struct {
	Id              int64 `gorm:"primaryKey"`
	Name            string
	Type            int64
	Assign          string
	TopImage        string
	TopImageSkipUrl string
}

type SpecialActivityInfo struct {
	Id      int64
	Periods int64
}

type Card struct {
	Id             int64 `gorm:"primaryKey"`
	CardName       string
	AutoAddType    int64
	AutoAddContent string
	Filter         []CardFilter `gorm:"-"`
}
type CardFilter struct {
	Id     int64
	Name   string
	CardId int64
}

type CardGoodsLive struct {
	Id         int64 `gorm:"primaryKey"`
	Cid        int64
	RelationId int64
	Channel    int64
	Title      string
	Type       int64
	Image      string
	CreatedAt  int64
}

type Periods struct {
	Id           int64
	Title        string
	Label        string
	ProductImg   string
	OnsaleStatus int64
}

type PeriodsInfo struct {
	Id              int64
	Title           string
	Label           string
	ProductImg      string
	OnsaleStatus    int64
	CountryId       []int64
	ChateauId       []int64
	ProducingAreaId []int64
	ProductType     []int64
	GrapeId         []int64
	Inventory       int64
	IsInventory     int64
}

type PackageData struct {
	Id                 int64
	AssociatedProducts string
}

type AssociatedProducts struct {
	ProductId interface{} `json:"product_id"`
	Nums      int64       `json:"nums"`
}

type Products struct {
	Id              int64 `gorm:"primaryKey"`
	CountryId       int64
	ProductType     int64
	ProducingAreaId string
	ChateauId       string
	Grape           string
}

type RegionsBase struct {
	Id            int64 `gorm:"primaryKey"`
	RegionsNameCn string
	ParentId      int64
}

type WineryBase struct {
	Id           int64 `gorm:"primaryKey"`
	WineryNameCn string
	ParentId     int64
}

type ProductType struct {
	Id   int64 `gorm:"primaryKey"`
	Name string
	Fid  int64
}

type AutoAddContent struct {
	Id   interface{} `json:"id"`
	Name interface{} `json:"name"`
}

type MarketingSection struct {
	Card       []Card
	Column     []Column
	Label      []RecommendLabel
	PeriodPnfo PeriodsInfo
}

type ColumnGoods struct {
	Id          int64 `gorm:"primaryKey"`
	Cid         int64
	Period      int64
	PeriodsType int64
	AddType     int64
	Title       string
	ShortName   string
	Image       string
	CreatedAt   int64
}

type Column struct {
	Id             int64 `gorm:"primaryKey"`
	Name           string
	PageMode       int64
	AddMethod      int64
	AutoAddType    int64
	AutoAddContent string
	Filter         []ColumnFilter `gorm:"-"`
}
type ColumnFilter struct {
	Id             int64
	Name           string
	CardId         int64
	AddMethod      int64
	AutoAddType    int64
	AutoAddContent string
}

type PeriodsProductInventory struct {
	Id        int64 `gorm:"primaryKey"`
	Period    int64
	ProductId int64
	ShortCode string
	Costprice float64
	Inventory int64
}

type RecommendLabel struct {
	Id             int64 `gorm:"primaryKey"`
	Name           string
	AutoAddType    string
	AutoAddContent string
}

type ColumnGoodsFilter struct {
	Id        int64 `gorm:"primaryKey"`
	GoodsId   int64
	CardId    int64
	FilterId  int64
	CreatedAt int64
}

type CardGoodsFilter struct {
	Id        int64 `gorm:"primaryKey"`
	GoodsId   int64
	CardId    int64
	FilterId  int64
	CreatedAt int64
}
