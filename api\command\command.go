package command

import (
	"engine/api/command/automatic_failure"
	"engine/api/command/inventory_notice"
	"engine/api/command/period_movesales_notice"
	"engine/api/command/purchase_order"
	"engine/api/command/tail_inventory"
	"engine/api/internal/svc"
)

// 定时任务
func NewTimedTasksLogic(svcCtx *svc.ServiceContext) {
	//拍品自动顶价机器人
	// go auctionbot.NewAuctionBotLogic(svcCtx.DbAuction, svcCtx.DbUser, svcCtx.Config, rdb_eleven)

	// 采购自动下单
	go purchase_order.NewPurchaseOrderLogic(svcCtx.DbCommodities, svcCtx.DbOrders, svcCtx.DbWms, svcCtx.Config, svcCtx.RDbSix)

	// 期数自动失效
	go automatic_failure.NewAutomaticFailureLogic(svcCtx.DbCommodities, svcCtx.Config)

	// 尾货库存页面（每天早上7点查询萌牙【闪购、秒发、食品】仓的库存总合小于等于6的简码，并展示出是否有在售期数，及剩余可售库存）
	go tail_inventory.NewTailInventoryLogic(svcCtx.DbCommodities, svcCtx.DbWms, svcCtx.Config, false)

	// 导出【闪购、秒发、食品】仓有库存简码，以表格形式每天早上7点推送到企微中台【杨文科、龙飞、陈泓州】
	go inventory_notice.NewInventoryNoticeLogic(svcCtx).Execute()

	// 导出【全频道在售库存动销】，以表格形式每天早上8点推送到企微中台
	go period_movesales_notice.NewPeriodMovesalesNoticeLogic(svcCtx).Execute()
}
