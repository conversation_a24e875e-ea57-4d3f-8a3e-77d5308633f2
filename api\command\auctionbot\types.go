package auctionbot

type Goods struct {
	Id                 int64 `gorm:"primaryKey"`
	Markup             float64
	SellTime           int64
	Price              float64
	ClosingAuctionTime int64
	CostPrice          float64
}

type BidRecord struct {
	Uid          string  `json:"uid"`
	BidPrice     float64 `json:"bid_price"`
	ProvinceName string  `json:"province_name"`
	CreateTime   int64   `json:"create_time"`
}

type VestUserInfo struct {
	Uid          int64
	Nickname     string
	AvatarImage  string
	ProvinceName string
}

type VestUser struct {
	Uid         int64
	Nickname    string
	AvatarImage string
	Region      string
}

type ProcessingData struct {
	Gs   Goods
	User map[int64]VestUserInfo
}
