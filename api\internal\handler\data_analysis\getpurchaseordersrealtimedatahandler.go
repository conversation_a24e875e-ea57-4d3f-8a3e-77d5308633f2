package data_analysis

import (
	"engine/api/internal/logic/data_analysis"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/zeromicro/go-zero/rest/httpx"
	"net/http"
)

func GetPurchaseOrdersRealTimeDataHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetPurchaseOrdersRealTimeDataReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := data_analysis.NewGetPurchaseOrdersRealTimeDataLogic(r.Context(), svcCtx)
		resp, err := l.GetPurchaseOrdersRealTimeData(&req)
		result.HttpResult(r, w, resp, err)
	}
}
