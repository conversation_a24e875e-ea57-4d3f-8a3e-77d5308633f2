package second

import (
	"context"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateFiltersLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateFiltersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateFiltersLogic {
	return &UpdateFiltersLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateFiltersLogic) UpdateFilters(req *types.UpdateFiltersReq) error {
	var (
		show_count int64
		data       PeriodsSecondFilters
	)
	//操作人ID
	uid := l.ctx.Value("uid").(int64)
	//操作人
	user_name := l.ctx.Value("name").(string)
	if !common.InArrayInt(req.Status, []int64{0, 1}) {
		return xerr.NewParamErrMsg("状态值错误")
	}
	//查询标签和标识是否存在
	l.svcCtx.DbCommodities.Model(&data).Where("id = ?", req.Id).Scan(&data)
	if data.Id == 0 {
		return xerr.NewParamErrMsg("标签ID错误")
	}

	if req.Status == 1 && data.FiltersType == 1 {
		// 查询显示中商品数量
		l.svcCtx.DbCommodities.
			Model(&PeriodsSecondFiltersGoods{}).
			Where("filters_id = ? and status = 1", req.Id).
			Count(&show_count)
		if show_count == 0 {
			return xerr.NewParamErrMsg("无显示商品，标签不可生效")
		}
	}

	timed := int64(time.Now().Unix())
	info := map[string]interface{}{
		"status":      req.Status,
		"operator_id": uid,
		"operator":    user_name,
		"update_time": timed,
	}
	err := l.svcCtx.DbCommodities.Model(&PeriodsSecondFilters{}).
		Where("id = ?", req.Id).
		Updates(info).Error
	if err != nil {
		return xerr.NewParamErrMsg(err.Error())
	}

	return nil
}
