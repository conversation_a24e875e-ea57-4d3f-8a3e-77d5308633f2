syntax = "v1"

info(
    title: "营销"
    author: "gangh"
    email: "<EMAIL>"
    version: "v3"
)

type (
    
    GetFullReductionActivityLabelReq {
        data []GetFullReductionActivityLabelReqData `json:"data" validate:"required" v:"数据"`
        
    }
    GetFullReductionActivityLabelReqData {
        Period int64 `json:"period" validate:"required" v:"期数"`
        PeriodType int64 `json:"period_type,optional" validate:"omitempty" v:"频道"`
    }

    GetFullReductionActivityLabelResp {
        Label map[int64][]string `json:"label"`
        Activity map[int64][]map[string]interface{} `json:"activity"`
    }

    AutomaticallyAddPeriodReq {
        Period int64 `json:"period" validate:"required" v:"期数"`
        PeriodType int64 `json:"period_type,optional" validate:"omitempty" v:"频道"`
        Title string `json:"title,optional" validate:"omitempty" v:"标题"`
        Card []int64 `json:"card,optional" validate:"omitempty" v:"卡片ID"`
        CardFilter []int64 `json:"card_filter,optional" validate:"omitempty" v:"卡片筛选项ID"`
        Column []int64 `json:"column,optional" validate:"omitempty" v:"栏目ID"`
        ColumnFilter []int64 `json:"column_filter,optional" validate:"omitempty" v:"栏目筛选项ID"`
        Label []int64 `json:"label,optional" validate:"omitempty" v:"标签ID"`
        Inventory int64 `json:"inventory,optional" validate:"omitempty" v:"是否操作库存"`  
    }

    GetMarketingSectionReq {
        Period int64 `json:"period" validate:"required" v:"期数"`
        PeriodType int64 `json:"period_type,optional" validate:"omitempty" v:"频道"`
    }
    GetMarketingSectionResp{
        Card []MarketingSection `json:"card"`
        Column []MarketingSection `json:"column"`
    }
    MarketingSection {
	    Id   int64  `json:"id"`
	    Name string `json:"name"`
        Filter []MarketingSectionFilter `json:"filter"`
    }
    MarketingSectionFilter {
        Id   int64  `json:"id"`
	    Name string `json:"name"`
    }

    SaleGoodsByRuleAddLabelReq{
        LabelId int64 `json:"label_id" validate:"required" v:"标签id"`
    }

    AutomaticallyAddAllPeriodReq {
        Card []int64 `json:"card,optional" validate:"omitempty" v:"卡片ID"`
        Column []int64 `json:"column,optional" validate:"omitempty" v:"栏目ID"`
        Label []int64 `json:"label,optional" validate:"omitempty" v:"标签ID"`
    }
)

@server(
    middleware: Global
    group : marketing
    prefix :/commodities_server/v3/marketing
)

service main {
    @handler GetFullReductionActivityLabel //获取满减活动标签
    post /GetFullReductionActivityLabel (GetFullReductionActivityLabelReq) returns (GetFullReductionActivityLabelResp)

    @handler AutomaticallyAddPeriod //根据规则自动添加商品到营销版块
    post /AutomaticallyAddPeriod (AutomaticallyAddPeriodReq)

    @handler GetMarketingSection //根据规则查询可自动添加的营销版块
    post /GetMarketingSection (GetMarketingSectionReq) returns (GetMarketingSectionResp)

    @handler SaleGoodsByRuleAddLabel //把所有的在售商品按照规则添加标签
    post /SaleGoodsByRuleAddLabel (SaleGoodsByRuleAddLabelReq)

    @handler AutomaticallyAddAllPeriod //把所有的在售商品按照规则自动添加商品到营销版块
    post /AutomaticallyAddAllPeriod (AutomaticallyAddAllPeriodReq)

}