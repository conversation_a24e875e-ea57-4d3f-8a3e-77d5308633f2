package second

type PeriodsSecondFilters struct {
	Id          int64 `gorm:"primaryKey"`
	Status      int64
	FiltersType int64
	Title       string
	Identifier  string
	WeightValue int64
	Operator    string
	UpdateTime  int64
}

type PeriodsSecondFiltersGoods struct {
	Id             int64 `gorm:"primaryKey"`
	Periods        int64
	PeriodsType    int64
	FiltersId      int64
	GoodsShortName string
	Status         int64
	WeightValue    int64
	UpdateName     string
	UpdateTime     int64
}

type PeriodsData struct {
	Id           int64
	Title        string
	Brief        string
	ProductImg   string
	OnsaleStatus int64
	Price        float64
	MarketPrice  float64
}
