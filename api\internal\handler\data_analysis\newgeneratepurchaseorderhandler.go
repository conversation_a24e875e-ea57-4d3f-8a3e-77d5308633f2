package data_analysis

import (
	"engine/common/result"
	"net/http"

	"engine/api/internal/logic/data_analysis"
	"engine/api/internal/svc"
)

func NewGeneratePurchaseOrderHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := data_analysis.NewNewGeneratePurchaseOrderLogic(r.Context(), svcCtx)
		err := l.NewGeneratePurchaseOrder()
		result.HttpResult(r, w, result.NullJson{}, err)
	}
}
