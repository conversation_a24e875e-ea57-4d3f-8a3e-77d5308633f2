package mysql

import (
	"engine/common/config"
	"engine/common/logger"
	"fmt"
	"log"
	"os"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	gormlogger "gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

func ListQuery(db *gorm.DB, page, limit int64, any interface{}) {
	offset := (page - 1) * limit
	db.Offset(int(offset)).Limit(int(limit)).Scan(any)
}

func Connect(cfg config.MysqlCfg, MaxIdleConns int) *gorm.DB {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%v)/%s?charset=utf8mb4&parseTime=True&loc=Local&timeout=%s&writeTimeout=%s&readTimeout=%s",
		cfg.User,
		cfg.Password,
		cfg.Host,
		cfg.Port,
		cfg.Database,
		"10s", //ConnTimeOut,
		"10s", //WriteTimeout,
		"10s", //ReadTimeout,
	)
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			TablePrefix:   cfg.Prefix,
			SingularTable: true, //表复数禁用
		},
		SkipDefaultTransaction: true, //关闭默认事务
		PrepareStmt:            true, // 开启缓存预编译，可以提高后续的调用速度
		QueryFields:            true, //自动将struct结构体的字段设置为查询字段
		Logger: gormlogger.New(log.New(os.Stdout, "\r\n", log.LstdFlags), gormlogger.Config{
			SlowThreshold:             3 * time.Second,
			Colorful:                  false,
			IgnoreRecordNotFoundError: true,
			LogLevel:                  gormlogger.Silent,
		}),
	})

	if err != nil {
		logger.E("DB Connect Error", err)
		panic(err)
	}

	//一个坑，不设置这个参数，gorm会把表名转义后加个s，导致找不到数据库的表
	SqlDB, _ := db.DB()
	// 设置连接池中最大的闲置连接数
	SqlDB.SetMaxIdleConns(MaxIdleConns)
	// 设置数据库的最大连接数量
	SqlDB.SetMaxOpenConns(MaxIdleConns)
	// 这是连接的最大可复用时间
	// SqlDB.SetConnMaxLifetime(time.Hour)

	return db
}
