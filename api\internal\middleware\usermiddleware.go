package middleware

import (
	"context"
	"encoding/base64"
	"engine/common/result"
	"engine/common/xerr"
	"net/http"

	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/rest/httpx"
)

type UserMiddleware struct {
}

func NewUserMiddleware() *UserMiddleware {
	return &UserMiddleware{}
}

func (m *UserMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		uid := cast.ToInt64(r.Header.Get("vinehoo-uid"))
		vos_name := cast.ToString(r.Header.Get("vinehoo-vos-name"))
		name_byte, _ := base64.StdEncoding.DecodeString(vos_name)
		if uid == 0 {
			httpx.WriteJson(w, 200, result.Error(xerr.UserNotExist, xerr.MapErrMsg(xerr.UserNotExist)))
			return
		}

		r = r.WithContext(context.WithValue(r.Context(), "uid", uid))
		r = r.<PERSON><PERSON>ontext(context.WithValue(r.Context(), "name", string(name_byte)))
		next(w, r)
	}
}
