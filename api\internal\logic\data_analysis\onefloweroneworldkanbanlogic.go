package data_analysis

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common"
	"engine/common/config"

	"github.com/zeromicro/go-zero/core/logx"
)

type OneFlowerOneWorldKanbanLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewOneFlowerOneWorldKanbanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *OneFlowerOneWorldKanbanLogic {
	return &OneFlowerOneWorldKanbanLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *OneFlowerOneWorldKanbanLogic) OneFlowerOneWorldKanban(req *types.OneFlowerOneWorldKanbanReq) (resp *types.OneFlowerOneWorldKanbanResp, err error) {
	resp = &types.OneFlowerOneWorldKanbanResp{
		List:  make([]types.OneFlowerOneWorldKanbanRespList, 0),
		Total: 0,
	}

	// 构建基础查询
	query := l.svcCtx.DbCommodities.Table("vh_purchase_orderno as po").
		Joins("LEFT JOIN vh_purchase_orderno_items as poi ON po.id = poi.purchase_orderno_id").
		Joins("LEFT JOIN vh_wiki.vh_products as p ON poi.short_code = p.short_code COLLATE utf8mb4_croatian_ci").
		Joins("LEFT JOIN vh_wiki.vh_product_unit as pu ON p.product_unit = pu.id").
		Where("po.corp_code = ? AND po.operate_status = ?", "032", 1)

	// 添加可选条件
	if req.ShortCode != "" {
		query = query.Where("poi.short_code = ?", req.ShortCode)
	}
	if req.WarehouseCode != "" {
		query = query.Where("po.warehouse_code = ?", req.WarehouseCode)
	}

	// 计算总记录数
	var (
		total  int64
		result []struct {
			Warehouse     string `gorm:"column:warehouse"`
			WarehouseCode string `gorm:"column:warehouse_code"`
			ShortCode     string `gorm:"column:short_code"`
			CnProductName string `gorm:"column:cn_product_name"`
			Capacity      string `gorm:"column:capacity"`
			UnitName      string `gorm:"column:name"`
			PurchaseNum   int64  `gorm:"column:purchase_num"`
		}
		short_code     []string
		warehouse_code []string
	)
	query.Count(&total)

	resp.Total = total

	if req.Page == 0 && req.Limit > 0 {
		req.Page = 1
	}
	if req.Limit == 0 && req.Page > 0 {
		req.Limit = 10
	}

	// 查询数据
	query = query.Select("po.warehouse, po.warehouse_code, poi.short_code, p.cn_product_name, p.capacity, pu.name, SUM(CAST(poi.number AS SIGNED)) as purchase_num").
		Group("po.warehouse_code, poi.short_code")
	if req.Page > 0 && req.Limit > 0 {
		// 计算分页偏移量
		offset := (req.Page - 1) * req.Limit
		query.Offset(int(offset)).Limit(int(req.Limit))
	}
	query.Scan(&result)

	for _, item := range result {
		if !common.InArrayStr(item.ShortCode, short_code) {
			short_code = append(short_code, item.ShortCode)
		}
		if !common.InArrayStr(item.WarehouseCode, warehouse_code) {
			warehouse_code = append(warehouse_code, item.WarehouseCode)
		}

	}
	if len(short_code) == 0 {
		return
	}

	// 查询销售数量
	sale_map := l.QuerySaleNum(short_code)

	// 查询调拨数量和现库存
	allocation_out_map, allocation_in_map, stock_num_map := l.QueryStockNum(warehouse_code, short_code)

	// 处理结果
	for _, item := range result {
		var (
			sale_num           int64
			allocation_out_num int64
			allocation_in_num  int64
			stock_num          int64
		)
		warehouse_code, _ := strconv.Atoi(item.WarehouseCode)
		r_key := fmt.Sprintf("%d:%s", warehouse_code, item.ShortCode)
		if val, ok := sale_map[r_key]; ok {
			sale_num = val
		}
		if val, ok := allocation_out_map[r_key]; ok {
			allocation_out_num = val
		}
		if val, ok := allocation_in_map[r_key]; ok {
			allocation_in_num = val
		}
		if val, ok := stock_num_map[r_key]; ok {
			stock_num = val
		}

		resp.List = append(resp.List, types.OneFlowerOneWorldKanbanRespList{
			Warehouse:        item.Warehouse,
			WarehouseCode:    item.WarehouseCode,
			ShortCode:        item.ShortCode,
			CnGoodsName:      item.CnProductName,
			Capacity:         item.Capacity,
			Unit:             item.UnitName,
			PurchaseNum:      item.PurchaseNum,
			SaleNum:          sale_num,
			AllocationOutNum: allocation_out_num,
			AllocationInNum:  allocation_in_num,
			InventoryNum:     stock_num,
		})
	}

	return resp, nil
}

func (l *OneFlowerOneWorldKanbanLogic) ExportOneFlowerOneWorldKanban(req *types.OneFlowerOneWorldKanbanReq) (resp *types.OneFlowerOneWorldKanbanResp, err error) {
	resp = &types.OneFlowerOneWorldKanbanResp{
		List:  make([]types.OneFlowerOneWorldKanbanRespList, 0),
		Total: 0,
	}

	// 构建基础查询
	query := l.svcCtx.DbCommodities.Table("vh_purchase_orderno as po").
		Joins("LEFT JOIN vh_purchase_orderno_items as poi ON po.id = poi.purchase_orderno_id").
		Joins("LEFT JOIN vh_wiki.vh_products as p ON poi.short_code = p.short_code COLLATE utf8mb4_croatian_ci").
		Joins("LEFT JOIN vh_wiki.vh_product_unit as pu ON p.product_unit = pu.id").
		Where("po.corp_code = ? AND po.operate_status = ?", "032", 1)

	// 添加可选条件
	if req.ShortCode != "" {
		query = query.Where("poi.short_code = ?", req.ShortCode)
	}
	if req.WarehouseCode != "" {
		query = query.Where("po.warehouse_code = ?", req.WarehouseCode)
	}

	// 计算总记录数
	var (
		total  int64
		result []struct {
			Warehouse     string `gorm:"column:warehouse"`
			WarehouseCode string `gorm:"column:warehouse_code"`
			ShortCode     string `gorm:"column:short_code"`
			CnProductName string `gorm:"column:cn_product_name"`
			Capacity      string `gorm:"column:capacity"`
			UnitName      string `gorm:"column:name"`
			PurchaseNum   int64  `gorm:"column:purchase_num"`
		}
		short_code     []string
		warehouse_code []string
	)
	query.Count(&total)

	resp.Total = total

	if req.Page == 0 && req.Limit > 0 {
		req.Page = 1
	}
	if req.Limit == 0 && req.Page > 0 {
		req.Limit = 10
	}

	// 查询数据
	query = query.Select("po.warehouse, po.warehouse_code, poi.short_code, p.cn_product_name, p.capacity, pu.name, SUM(CAST(poi.number AS SIGNED)) as purchase_num").
		Group("po.warehouse_code, poi.short_code")
	if req.Page > 0 && req.Limit > 0 {
		// 计算分页偏移量
		offset := (req.Page - 1) * req.Limit
		query.Offset(int(offset)).Limit(int(req.Limit))
	}
	query.Scan(&result)

	for _, item := range result {
		if !common.InArrayStr(item.ShortCode, short_code) {
			short_code = append(short_code, item.ShortCode)
		}
		if !common.InArrayStr(item.WarehouseCode, warehouse_code) {
			warehouse_code = append(warehouse_code, item.WarehouseCode)
		}

	}
	if len(short_code) == 0 {
		return
	}

	// 查询销售数量
	sale_map := l.QuerySaleNum(short_code)

	// 查询调拨数量和现库存
	allocation_out_map, allocation_in_map, stock_num_map := l.QueryStockNum(warehouse_code, short_code)

	// 处理结果
	for _, item := range result {
		var (
			sale_num           int64
			allocation_out_num int64
			allocation_in_num  int64
			stock_num          int64
		)
		warehouse_code, _ := strconv.Atoi(item.WarehouseCode)
		r_key := fmt.Sprintf("%d:%s", warehouse_code, item.ShortCode)
		if val, ok := sale_map[r_key]; ok {
			sale_num = val
		}
		if val, ok := allocation_out_map[r_key]; ok {
			allocation_out_num = val
		}
		if val, ok := allocation_in_map[r_key]; ok {
			allocation_in_num = val
		}
		if val, ok := stock_num_map[r_key]; ok {
			stock_num = val
		}

		resp.List = append(resp.List, types.OneFlowerOneWorldKanbanRespList{
			Warehouse:        item.Warehouse,
			WarehouseCode:    item.WarehouseCode,
			ShortCode:        item.ShortCode,
			CnGoodsName:      item.CnProductName,
			Capacity:         item.Capacity,
			Unit:             item.UnitName,
			PurchaseNum:      item.PurchaseNum,
			SaleNum:          sale_num,
			AllocationOutNum: allocation_out_num,
			AllocationInNum:  allocation_in_num,
			InventoryNum:     stock_num,
		})
	}

	return resp, nil
}

// 查询销售数量
func (l *OneFlowerOneWorldKanbanLogic) QuerySaleNum(short_code []string) map[string]int64 {
	var (
		lc sync.Mutex
		wg sync.WaitGroup
	)

	result := make(map[string]int64)
	// 查询销售数量
	type PeriodsProductOrderData struct {
		Period             int64
		OrderQty           int64
		AssociatedProducts string
		ProductInfo        string
	}
	type AssociatedProductsDate struct {
		ProductId int64 `json:"product_id"`
		Nums      int64 `json:"nums"`
	}
	type PeriodData struct {
		Period    int64
		ProductId int64
		ShortCode string
		ErpId     string
	}
	for k, period_table := range config.PeriodTable {
		wg.Add(1)
		go func(k int, period_table string) {
			var (
				data        []PeriodsProductOrderData
				period_data []PeriodData
				period_ids  []int64
			)
			defer wg.Done()
			order_table := config.OrderTable[k]
			package_table := config.PackageTable[k]

			// 查询期数和产品
			l.svcCtx.DbCommodities.Table(period_table+" as p").
				Joins("left join vh_periods_product_inventory as poi on poi.period = p.id").
				Where("p.payee_merchant_id = 10 and poi.short_code IN ?", short_code).
				Select("poi.period,poi.product_id,poi.short_code,poi.erp_id").
				Scan(&period_data)
			if len(period_data) == 0 {
				return
			}
			period_data_map := make(map[string]PeriodData)
			for _, v := range period_data {
				key := fmt.Sprintf("%d:%d", v.Period, v.ProductId)
				period_data_map[key] = v
				period_ids = append(period_ids, v.Period)
			}

			// 查询订单信息
			l.svcCtx.DbCommodities.Table("vh_orders."+order_table+" as o").
				Joins(fmt.Sprintf("left join vh_commodities.%s as pkg on pkg.id = o.package_id", package_table)).
				Joins("left join vh_orders.vh_order_main as om on om.id = o.main_order_id").
				Joins("left join vh_orders.vh_order_mystery_box_log as box on box.main_order_no = om.main_order_no").
				Where("o.period IN ? and o.sub_order_status IN(1,2,3)", period_ids).
				Select("o.period,o.order_qty,pkg.associated_products,box.product_info").
				Group("o.id").Scan(&data)
			for _, v := range data {
				var pkg_products []AssociatedProductsDate
				if v.ProductInfo != "" {
					json.Unmarshal([]byte(v.ProductInfo), &pkg_products)
				} else {
					json.Unmarshal([]byte(v.AssociatedProducts), &pkg_products)
				}
				for _, p := range pkg_products {
					num := int64(p.Nums * v.OrderQty)

					key := fmt.Sprintf("%d:%d", v.Period, p.ProductId)
					if val, ok := period_data_map[key]; ok {
						lc.Lock()
						warehouse_code, _ := strconv.Atoi(val.ErpId)
						r_key := fmt.Sprintf("%d:%s", warehouse_code, val.ShortCode)
						if _, ok := result[r_key]; !ok {
							result[r_key] = 0
						}
						result[r_key] = result[r_key] + num
						lc.Unlock()
					}

				}

			}
		}(k, period_table)
	}
	wg.Wait()

	return result
}

// 查询调拨数量和现库存
func (l *OneFlowerOneWorldKanbanLogic) QueryStockNum(warehouse_code []string, short_code []string) (allocation_out map[string]int64, allocation_in map[string]int64, stock_num map[string]int64) {
	allocation_out = make(map[string]int64)
	stock_num = make(map[string]int64)
	allocation_in = make(map[string]int64)
	type AllocationLog struct {
		AllocationId           int64
		ShortCode              string
		FictitiousId           int64
		AllocationFictitiousId int64
		Number                 int64
	}
	type FictitiousGoods struct {
		FictitiousId int64
		ShortCode    string
		GoodsCount   int64
	}
	var (
		allocation_log   []AllocationLog
		fictitious_goods []FictitiousGoods
		wg               sync.WaitGroup
	)

	wg.Add(1)
	go func() {
		defer wg.Done()
		l.svcCtx.DbWms.Table("wms_allocation_log as l").
			Joins("left join wms_goods g on l.bar_code = g.bar_code").
			Where("(l.fictitious_id IN ? and l.corp = 10) or (l.allocation_fictitious_id IN ? and l.allocation_corp = 10)", warehouse_code, warehouse_code).
			Where(" g.short_code IN ?", short_code).
			Select("l.allocation_id,g.short_code,l.fictitious_id,l.allocation_fictitious_id,l.number").
			Scan(&allocation_log)

		for _, v := range allocation_log {
			in_key := fmt.Sprintf("%d:%s", v.AllocationFictitiousId, v.ShortCode)
			out_key := fmt.Sprintf("%d:%s", v.FictitiousId, v.ShortCode)

			if _, ok := allocation_in[in_key]; !ok {
				allocation_in[in_key] = 0
			}
			if _, ok := allocation_out[out_key]; !ok {
				allocation_out[out_key] = 0
			}
			allocation_in[in_key] = allocation_in[in_key] + v.Number
			allocation_out[out_key] = allocation_out[out_key] + v.Number
		}
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		l.svcCtx.DbWms.Table("wms_fictitious_goods as fg").
			Joins("left join wms_goods g on g.bar_code = fg.bar_code").
			Where("fg.fictitious_id IN ? and fg.corp = 10 and g.short_code IN ? and fg.goods_count > 0", warehouse_code, short_code).
			Select("fg.fictitious_id,g.short_code,fg.goods_count").
			Scan(&fictitious_goods)
		for _, v := range fictitious_goods {
			key := fmt.Sprintf("%d:%s", v.FictitiousId, v.ShortCode)
			if _, ok := stock_num[key]; !ok {
				stock_num[key] = 0
			}
			stock_num[key] = stock_num[key] + v.GoodsCount
		}
	}()
	wg.Wait()

	return
}
