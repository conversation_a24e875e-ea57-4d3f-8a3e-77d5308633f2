package marketing

type Reduction struct {
	Id              int64 `gorm:"primaryKey"`
	Name            string
	Type            int64
	Assign          string
	TopImage        string
	TopImageSkipUrl string
}

type SpecialActivityInfo struct {
	Id      int64
	Periods int64
}

type Card struct {
	Id             int64 `gorm:"primaryKey"`
	CardName       string
	AutoAddType    int64
	AutoAddContent string
}

type CardGoodsLive struct {
	Id  int64 `gorm:"primaryKey"`
	Cid int64
}

type PeriodsInfo struct {
	Id              int64
	Title           string
	Label           string
	CountryId       []int64
	ChateauId       []int64
	ProducingAreaId []int64
	ProductType     []int64
}

type PackageData struct {
	Id                 int64
	AssociatedProducts string
}

type AssociatedProducts struct {
	ProductId interface{} `json:"product_id"`
	Nums      int64       `json:"nums"`
}

type Products struct {
	Id              int64 `gorm:"primaryKey"`
	CountryId       int64
	ProductType     int64
	ProducingAreaId int64
	ChateauId       int64
}

type RegionsBase struct {
	Id            int64 `gorm:"primaryKey"`
	RegionsNameCn string
	ParentId      int64
}

type WineryBase struct {
	Id           int64 `gorm:"primaryKey"`
	WineryNameCn string
	ParentId     int64
}

type ProductType struct {
	Id   int64 `gorm:"primaryKey"`
	Name string
	Fid  int64
}

type AutoAddContent struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
}

type CommonInfo struct {
	Id    int64
	Title string
}
