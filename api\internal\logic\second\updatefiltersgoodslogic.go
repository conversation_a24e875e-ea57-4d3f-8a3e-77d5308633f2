package second

import (
	"context"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateFiltersGoodsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateFiltersGoodsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateFiltersGoodsLogic {
	return &UpdateFiltersGoodsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateFiltersGoodsLogic) UpdateFiltersGoods(req *types.UpdateFiltersGoodsReq) error {
	var (
		goods_info   PeriodsSecondFiltersGoods
		table_name   string
		periods_info PeriodsData
	)
	// 操作人ID
	uid := l.ctx.Value("uid").(int64)
	// 操作人
	user_name := l.ctx.Value("name").(string)
	// 查询商品信息
	l.svcCtx.DbCommodities.Model(&goods_info).Where("id = ? ", req.Id).Take(&goods_info)
	if goods_info.Id == 0 {
		return xerr.NewParamErrMsg("商品信息不存在")
	}

	update := map[string]interface{}{
		"update_id":   uid,
		"update_name": user_name,
		"update_time": int64(time.Now().Unix()),
	}
	switch req.OperationType {
	case 0: // 更新状态
		if req.Value == 1 {
			// 查询期数信息
			switch goods_info.PeriodsType {
			case 0: // 闪购
				table_name = "vh_periods_flash"
			case 1: // 秒发
				table_name = "vh_periods_second"
			}
			l.svcCtx.DbCommodities.
				Table(table_name).
				Where("id = ?", goods_info.Periods).
				Select("id", "onsale_status").
				Scan(&periods_info)

			if periods_info.Id == 0 {
				return xerr.NewParamErrMsg("期数不存在")
			}
			if periods_info.OnsaleStatus != 2 {
				return xerr.NewParamErrMsg("只有在售中期数可显示")
			}
		}
		update["status"] = req.Value

	case 1: // 更新权重值
		update["weight_value"] = req.Value
	}

	err := l.svcCtx.DbCommodities.Model(&goods_info).Where("id = ?", goods_info.Id).Updates(update).Error
	if err != nil {
		return xerr.NewParamErrMsg(err.Error())
	}

	if req.OperationType == 0 && req.Value == 0 {
		// 自动判断筛选标签失效
		go AutomaticJudgmentFiltersFailure(l.svcCtx.DbCommodities, goods_info.FiltersId)
	}

	return nil
}
