package tail_inventory

type FictitiousGoods struct {
	GoodsName      string
	EnGoodsName    string
	ShortCode      string
	BarCode        string
	FictitiousId   int64
	FictitiousName string
	GoodsCount     int64
	Inventory      []FictitiousGoodsInventory `gorm:"-"`
}

type FictitiousGoodsInventory struct {
	FictitiousId   int64  `json:"fictitious_id"`
	FictitiousName string `json:"fictitious_name"`
	GoodsCount     int64  `json:"goods_count"`
	CorpName       string `json:"corp_name"`
}

type PeriodsProductInventory struct {
	Id          int64 `gorm:"primaryKey"`
	Period      int64
	PeriodsType int64
	ProductId   int64
	ShortCode   string
	Inventory   int64
	Costprice   float64
}

type PeriodsInfo struct {
	Id           int64
	OnsaleStatus int64
	SellTime     int64
	Supplier     string
	BuyerName    string
}

type TailInventoryWms struct {
	Id                     int64 `gorm:"primaryKey"`
	CnGoodsName            string
	EnGoodsName            string
	ShortCode              string
	BarCode                string
	Inventory              string
	IsSale                 int64
	Period                 string
	PeriodInfo             string
	RemainingSaleInventory string
	CreatedTime            int64
}

type RemainingSaleInventory struct {
	Period    int64 `json:"period"`
	Inventory int64 `json:"inventory"`
}

type PackageInfo struct {
	PackageName string  `json:"package_name"`
	Price       float64 `json:"price"`
}

type PeriodData struct {
	Id        int64         `json:"id"`
	SellTime  string        `json:"sell_time"`
	Supplier  string        `json:"supplier"`
	BuyerName string        `json:"buyer_name"`
	Costprice float64       `json:"costprice"`
	Package   []PackageInfo `json:"package"`
}
