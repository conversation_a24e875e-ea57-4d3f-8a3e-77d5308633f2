package second

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteFiltersGoodsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteFiltersGoodsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteFiltersGoodsLogic {
	return &DeleteFiltersGoodsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteFiltersGoodsLogic) DeleteFiltersGoods(req *types.DeleteFiltersGoodsReq) error {
	var goods_info PeriodsSecondFiltersGoods
	// 查询商品信息
	l.svcCtx.DbCommodities.Model(&goods_info).Where("id = ? ", req.Id).Take(&goods_info)
	if goods_info.Id == 0 {
		return xerr.NewParamErrMsg("商品信息不存在")
	}
	// 删除
	err := l.svcCtx.DbCommodities.Delete(&goods_info).Error
	if err != nil {
		return xerr.NewParamErrMsg(err.Error())
	}
	// 自动判断筛选标签失效
	go AutomaticJudgmentFiltersFailure(l.svcCtx.DbCommodities, goods_info.FiltersId)

	return nil
}
